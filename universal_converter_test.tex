\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{amsfonts}

\begin{document}

\title{通用方程式转换器测试}
\author{Universal Equation Converter}
\date{\today}
\maketitle

\section{转换示例}

\subsection{标准方程式格式}
\begin{equation}
dq t = M - 1 p_{t} \, \quad dt, \quad dp t = - \nabla q_{V} (q_{t} ) \, \quad dt - \gammaM - 1 p_{t} \, \quad dt + 2\gamma\beta - 1 \, \quad dW t
\label{eq:1}
\end{equation}

\subsection{对齐方程式格式}
\begin{align}
dq t = M - 1 p_{t} \ \\
dt \\
dp t = - \nabla q_{V} (q_{t} ) \ \\
dt - \gammaM - 1 p_{t} \ \\
dt + 2\gamma\beta - 1 \ \\
dW t
\label{eq:2}
\end{align}

\subsection{分段函数格式}
\begin{equation}
\begin{cases}
dq t = M - 1 p_{t} \ \\
dt \\
dp t = - \nabla q_{V} (q_{t} ) \ \\
dt - \gammaM - 1 p_{t} \ \\
dt + 2\gamma\beta - 1 \ \\
dW t
\end{cases}
\label{eq:3}
\end{equation}

\section{Unicode字符测试}

希腊字母：$\alpha + \beta = \gamma$

数学符号：$\partialf/\partialx + \nablaf = \infty$

集合符号：$x \in \mathbb{R}, \quad y \notin \emptyset$

\end{document}