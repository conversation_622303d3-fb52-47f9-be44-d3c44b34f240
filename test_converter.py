#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON到LaTeX转换器
"""

from json_to_latex_converter import JSONToLaTeXConverter
import json
from pathlib import Path

def test_conversion():
    """测试转换功能"""
    print("🧪 测试JSON到LaTeX转换器")
    print("=" * 50)
    
    # 创建转换器
    converter = JSONToLaTeXConverter()
    
    # 测试文件路径
    input_file = "output_dir/mcf_analytical_presentation.json"
    output_file = "output_dir/mcf_analytical_presentation_test.tex"
    
    # 检查输入文件
    if not Path(input_file).exists():
        print(f"❌ 错误：找不到输入文件 {input_file}")
        return False
    
    try:
        # 执行转换
        print(f"📄 正在转换: {input_file}")
        result_file = converter.convert_file(
            input_file=input_file,
            output_file=output_file,
            include_metadata=True,
            document_class="article"
        )
        
        # 验证输出文件
        if Path(result_file).exists():
            print(f"✅ 转换成功！生成文件: {result_file}")
            
            # 读取并分析生成的文件
            with open(result_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 基本验证
            checks = [
                ("文档类声明", r"\documentclass" in content),
                ("UTF-8编码", r"\usepackage[utf8]{inputenc}" in content),
                ("法语支持", r"\usepackage[french]{babel}" in content),
                ("数学包", r"\usepackage{amsmath}" in content),
                ("文档开始", r"\begin{document}" in content),
                ("文档结束", r"\end{document}" in content),
                ("标题", r"\title{" in content),
                ("作者", r"\author{" in content),
                ("摘要", r"\begin{abstract}" in content),
            ]
            
            print("\n📋 验证结果:")
            all_passed = True
            for check_name, passed in checks:
                status = "✅" if passed else "❌"
                print(f"   {status} {check_name}")
                if not passed:
                    all_passed = False
            
            # 统计信息
            lines = content.split('\n')
            print(f"\n📊 文件统计:")
            print(f"   总行数: {len(lines)}")
            print(f"   文件大小: {len(content)} 字符")
            
            # 显示前几行
            print(f"\n📄 文件预览 (前10行):")
            for i, line in enumerate(lines[:10], 1):
                print(f"   {i:2d}: {line}")
            
            if all_passed:
                print(f"\n🎉 所有检查通过！转换成功完成。")
                return True
            else:
                print(f"\n⚠️  部分检查失败，请检查生成的文件。")
                return False
                
        else:
            print(f"❌ 错误：输出文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_unicode_conversion():
    """测试Unicode字符转换"""
    print("\n🔤 测试Unicode字符转换")
    print("-" * 30)
    
    converter = JSONToLaTeXConverter()
    
    test_cases = [
        ("法语字符", "École d'été", r"'{E}cole d''{e}t'{e}"),
        ("数学符号", "α + β = γ", r"\alpha + \beta = \gamma"),
        ("特殊字符", "résumé", r"r'{e}sum'{e}"),
        ("混合内容", "Université ∈ ℝ", r"Universit'{e} \in \mathbb{R}"),
    ]
    
    for test_name, input_text, expected_pattern in test_cases:
        result = converter.clean_text(input_text)
        print(f"   {test_name}:")
        print(f"     输入: {input_text}")
        print(f"     输出: {result}")
        # 简单检查是否包含预期的模式
        if any(char in result for char in [r'\'{', r'\`{', r'\^{', r'\\alpha', r'\\beta']):
            print(f"     状态: ✅ 转换成功")
        else:
            print(f"     状态: ⚠️  可能需要检查")
        print()

if __name__ == "__main__":
    # 运行测试
    success = test_conversion()
    test_unicode_conversion()
    
    if success:
        print("\n🎯 建议下一步:")
        print("   1. 使用pdflatex编译生成的.tex文件")
        print("   2. 检查PDF输出是否符合预期")
        print("   3. 如需要，手动调整LaTeX代码")
    else:
        print("\n🔧 故障排除建议:")
        print("   1. 检查输入JSON文件格式")
        print("   2. 确保所有依赖包已安装")
        print("   3. 查看详细错误信息")
