# 通用JSON到LaTeX方程式转换器

## 📋 概述

这是一个功能强大的通用转换器，可以将包含Unicode数学符号的JSON方程式数据转换为标准的LaTeX格式。特别适用于处理从PDF解析工具（如GROBID）生成的数学表达式。

## 🚀 主要特性

- ✅ **完整的Unicode支持**：支持所有常见的数学Unicode字符
- ✅ **多种输出格式**：equation、align、cases、inline
- ✅ **智能格式化**：自动处理下标、上标、函数调用
- ✅ **批量处理**：支持批量转换多个方程式
- ✅ **简单易用**：提供简洁的API接口

## 📦 文件说明

- `universal_equation_converter.py` - 主要的转换器模块
- `equation_converter.py` - 完整的类实现版本
- `json_to_latex_converter.py` - 原始的JSON文档转换器

## 🔧 使用方法

### 基本使用

```python
from universal_equation_converter import convert_equation_to_latex

# 转换单个方程式
equation = "dq t = M \u22121 p t dt"
latex_code = convert_equation_to_latex(equation, "(1)")
print(latex_code)
```

### JSON数据转换

```python
from universal_equation_converter import convert_json_equation

# 从JSON数据转换
eq_data = {
    "raw_str": "dq t = M \u22121 p t dt, dp t = \u2212\u2207 q V (q t ) dt",
    "eq_num": "(4)"
}

latex_code = convert_json_equation(eq_data)
print(latex_code)
```

### 不同输出格式

```python
# 标准方程式格式
latex_eq = convert_equation_to_latex(equation, "(1)", "equation")

# 对齐方程式格式（适合多行）
latex_align = convert_equation_to_latex(equation, "(1)", "align")

# 分段函数格式
latex_cases = convert_equation_to_latex(equation, "(1)", "cases")

# 行内数学模式
latex_inline = convert_equation_to_latex(equation, equation_type="inline")
```

### 批量转换

```python
from universal_equation_converter import batch_convert_equations

equations = [
    {"raw_str": "\u03b1 + \u03b2 = \u03b3", "eq_num": "(1)"},
    {"raw_str": "x\u00b2 + y\u00b2 = z\u00b2", "eq_num": "(2)"},
    "E = mc\u00b2"  # 也可以直接传入字符串
]

results = batch_convert_equations(equations)
for result in results:
    print(result)
```

## 📚 支持的Unicode字符

### 希腊字母
- 小写：α β γ δ ε ζ η θ ι κ λ μ ν ξ π ρ σ τ υ φ χ ψ ω
- 大写：Γ Δ Θ Λ Ξ Π Σ Υ Φ Ψ Ω

### 数学运算符
- 基本：± × ÷ ≠ ≤ ≥ ≈ ≡ ∝ ∞
- 微积分：∂ ∇ ∫ ∬ ∭ ∑ ∏
- 集合：∈ ∉ ⊂ ⊃ ⊆ ⊇ ∩ ∪ ∅ ∀ ∃

### 箭头符号
- → ← ↔ ⇒ ⇐ ⇔

### 上标和下标
- 上标数字：⁰ ¹ ² ³ ⁴ ⁵ ⁶ ⁷ ⁸ ⁹ ⁺ ⁻
- 下标数字：₀ ₁ ₂ ₃ ₄ ₅ ₆ ₇ ₈ ₉

### 特殊符号
- 数学集合：ℂ ℕ ℚ ℝ ℤ
- 其他：√ … ⋯

## 🎯 转换示例

### 输入（JSON格式）
```json
{
    "raw_str": "dq t = M \u22121 p t dt, dp t = \u2212\u2207 q V (q t ) dt \u2212 \u03b3M \u22121 p t dt + 2\u03b3\u03b2 \u22121 dW t .",
    "eq_num": "(4)"
}
```

### 输出（LaTeX格式）
```latex
\begin{equation}
dq_t = M^{-1} p_t \, dt, \quad dp_t = -\nabla_q V(q_t) \, dt - \gamma M^{-1} p_t \, dt + \sqrt{2\gamma\beta^{-1}} \, dW_t
\label{eq:4}
\end{equation}
```

## 🔧 高级功能

### 自动格式化规则

1. **下标处理**：`q t` → `q_t`
2. **上标处理**：`M⁻¹` → `M^{-1}`
3. **函数调用**：`V (q t )` → `V(q_t)`
4. **微分符号**：`dt` → `\, dt`
5. **平方根**：`2γβ⁻¹` → `\sqrt{2\gamma\beta^{-1}}`
6. **间距优化**：自动添加适当的数学间距

### 错误处理

转换器具有良好的容错性：
- 无法识别的Unicode字符会保持原样
- 格式错误的输入会尽可能修复
- 提供详细的转换日志

## 💡 使用建议

1. **编译LaTeX**：生成的代码需要以下包：
   ```latex
   \usepackage{amsmath}
   \usepackage{amssymb}
   \usepackage{amsfonts}
   ```

2. **复杂方程式**：对于非常复杂的方程式，建议使用 `align` 格式

3. **批量处理**：处理大量方程式时使用 `batch_convert_equations`

4. **自定义格式**：可以根据需要修改 `unicode_map` 添加更多符号

## 🐛 故障排除

### 常见问题

1. **字符显示异常**：确保输入是正确的Unicode编码
2. **LaTeX编译错误**：检查是否包含了必要的数学包
3. **格式不正确**：尝试不同的 `equation_type` 参数

### 调试技巧

```python
# 查看转换过程
equation = "your_equation_here"
print(f"原始: {equation}")
result = convert_equation_to_latex(equation)
print(f"转换后: {result}")
```

## 📞 支持

如果遇到问题或需要添加新功能，请：
1. 检查输入数据格式
2. 确认Unicode字符是否在支持列表中
3. 尝试不同的输出格式
4. 查看生成的LaTeX代码是否正确
