#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例脚本：使用JSON到LaTeX转换器
演示如何将mcf_analytical_presentation.json转换为LaTeX格式
"""

from json_to_latex_converter import JSONToLaTeXConverter
import json
from pathlib import Path

def main():
    # 创建转换器实例
    converter = JSONToLaTeXConverter()
    
    # 输入和输出文件路径
    input_file = "output_dir/mcf_analytical_presentation.json"
    output_file = "output_dir/mcf_analytical_presentation.tex"
    
    print("开始转换JSON到LaTeX...")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    
    try:
        # 检查输入文件是否存在
        if not Path(input_file).exists():
            print(f"错误：输入文件不存在 - {input_file}")
            return
        
        # 执行转换
        result_file = converter.convert_file(
            input_file=input_file,
            output_file=output_file,
            include_metadata=True,  # 包含标题、作者等元数据
            document_class="article"  # 使用article文档类
        )
        
        print(f"✅ 转换成功！")
        print(f"LaTeX文件已生成: {result_file}")
        
        # 显示一些统计信息
        with open(input_file, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        # 统计信息
        title = json_data.get('title', 'N/A')
        authors = json_data.get('authors', [])
        body_text = json_data.get('pdf_parse', {}).get('body_text', [])
        
        print(f"\n📊 文档信息:")
        print(f"   标题: {title}")
        print(f"   作者数量: {len(authors)}")
        print(f"   正文段落数: {len(body_text)}")
        
        # 显示生成的LaTeX文件的前几行
        print(f"\n📄 生成的LaTeX文件预览 (前20行):")
        with open(result_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines[:20], 1):
                print(f"{i:2d}: {line.rstrip()}")
        
        if len(lines) > 20:
            print(f"... (还有 {len(lines) - 20} 行)")
        
        print(f"\n💡 提示:")
        print(f"   1. 您可以使用LaTeX编译器（如pdflatex）编译生成的.tex文件")
        print(f"   2. 命令示例: pdflatex {output_file}")
        print(f"   3. 如果需要处理法语字符，确保使用UTF-8编码")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
