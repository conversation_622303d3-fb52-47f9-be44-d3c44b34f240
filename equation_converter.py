#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用的JSON到LaTeX方程式转换器
支持各种数学表达式和符号的转换
"""

import re
import json
from typing import Dict, List, Any, Optional

class UniversalEquationConverter:
    def __init__(self):
        """初始化通用方程式转换器"""
        # 完整的Unicode到LaTeX映射表
        self.unicode_to_latex = {
            # 希腊字母 (小写)
            '\u03b1': r'\alpha', '\u03b2': r'\beta', '\u03b3': r'\gamma', '\u03b4': r'\delta',
            '\u03b5': r'\epsilon', '\u03b6': r'\zeta', '\u03b7': r'\eta', '\u03b8': r'\theta',
            '\u03b9': r'\iota', '\u03ba': r'\kappa', '\u03bb': r'\lambda', '\u03bc': r'\mu',
            '\u03bd': r'\nu', '\u03be': r'\xi', '\u03bf': r'o', '\u03c0': r'\pi',
            '\u03c1': r'\rho', '\u03c2': r'\varsigma', '\u03c3': r'\sigma', '\u03c4': r'\tau',
            '\u03c5': r'\upsilon', '\u03c6': r'\phi', '\u03c7': r'\chi', '\u03c8': r'\psi',
            '\u03c9': r'\omega',

            # 希腊字母 (大写)
            '\u0391': r'A', '\u0392': r'B', '\u0393': r'\Gamma', '\u0394': r'\Delta',
            '\u0395': r'E', '\u0396': r'Z', '\u0397': r'H', '\u0398': r'\Theta',
            '\u0399': r'I', '\u039a': r'K', '\u039b': r'\Lambda', '\u039c': r'M',
            '\u039d': r'N', '\u039e': r'\Xi', '\u039f': r'O', '\u03a0': r'\Pi',
            '\u03a1': r'P', '\u03a3': r'\Sigma', '\u03a4': r'T', '\u03a5': r'\Upsilon',
            '\u03a6': r'\Phi', '\u03a7': r'X', '\u03a8': r'\Psi', '\u03a9': r'\Omega',

            # 数学运算符
            '\u2202': r'\partial', '\u2207': r'\nabla', '\u2212': r'-', '\u2213': r'\mp',
            '\u00b1': r'\pm', '\u00d7': r'\times', '\u00f7': r'\div', '\u2260': r'\neq',
            '\u2264': r'\leq', '\u2265': r'\geq', '\u226a': r'\ll', '\u226b': r'\gg',
            '\u2248': r'\approx', '\u2261': r'\equiv', '\u221d': r'\propto', '\u221e': r'\infty',
            '\u2211': r'\sum', '\u220f': r'\prod', '\u222b': r'\int', '\u222c': r'\iint',
            '\u222d': r'\iiint', '\u222e': r'\oint',

            # 集合论符号
            '\u2208': r'\in', '\u2209': r'\notin', '\u2282': r'\subset', '\u2283': r'\supset',
            '\u2286': r'\subseteq', '\u2287': r'\supseteq', '\u2229': r'\cap', '\u222a': r'\cup',
            '\u2205': r'\emptyset', '\u2200': r'\forall', '\u2203': r'\exists',

            # 箭头
            '\u2192': r'\rightarrow', '\u2190': r'\leftarrow', '\u2194': r'\leftrightarrow',
            '\u21d2': r'\Rightarrow', '\u21d0': r'\Leftarrow', '\u21d4': r'\Leftrightarrow',
            '\u2197': r'\nearrow', '\u2198': r'\searrow', '\u2199': r'\swarrow', '\u2196': r'\nwarrow',

            # 上标数字
            '\u2070': r'^{0}', '\u00b9': r'^{1}', '\u00b2': r'^{2}', '\u00b3': r'^{3}',
            '\u2074': r'^{4}', '\u2075': r'^{5}', '\u2076': r'^{6}', '\u2077': r'^{7}',
            '\u2078': r'^{8}', '\u2079': r'^{9}', '\u207a': r'^{+}', '\u207b': r'^{-}',

            # 下标数字
            '\u2080': r'_{0}', '\u2081': r'_{1}', '\u2082': r'_{2}', '\u2083': r'_{3}',
            '\u2084': r'_{4}', '\u2085': r'_{5}', '\u2086': r'_{6}', '\u2087': r'_{7}',
            '\u2088': r'_{8}', '\u2089': r'_{9}', '\u208a': r'_{+}', '\u208b': r'_{-}',

            # 特殊符号
            '\u2026': r'\ldots', '\u22ef': r'\cdots', '\u22ee': r'\vdots', '\u22f1': r'\ddots',
            '\u00a0': ' ', '\u2009': r'\,', '\u200a': r'\,', '\u2032': r"'", '\u2033': r"''",
            '\u2034': r"'''", '\u2057': r"''''",

            # 分数和根号
            '\u221a': r'\sqrt', '\u221b': r'\sqrt[3]', '\u221c': r'\sqrt[4]',

            # 其他常用符号
            '\u2135': r'\aleph', '\u2136': r'\beth', '\u2137': r'\gimel', '\u2138': r'\daleth',
            '\u2102': r'\mathbb{C}', '\u2115': r'\mathbb{N}', '\u211a': r'\mathbb{Q}',
            '\u211d': r'\mathbb{R}', '\u2124': r'\mathbb{Z}',
        }

    def convert_unicode_to_latex(self, text: str) -> str:
        """将Unicode数学符号转换为LaTeX命令"""
        # 处理特殊的上标-1组合
        text = re.sub(r'([a-zA-Z0-9}])\s*⁻¹', r'\1^{-1}', text)
        text = re.sub(r'([a-zA-Z0-9}])\s*\u207b\u00b9', r'\1^{-1}', text)

        # 替换Unicode字符
        for unicode_char, latex_cmd in self.unicode_to_latex.items():
            text = text.replace(unicode_char, latex_cmd)

        return text

    def process_subscripts_and_superscripts(self, text: str) -> str:
        """处理下标和上标"""
        # 处理变量后跟单个字母/数字的情况 (下标)
        text = re.sub(r'\b([a-zA-Z])\s+([a-zA-Z0-9])\b', r'\1_{\2}', text)

        # 处理特殊的数学变量下标
        text = re.sub(r'\b([a-zA-Z])\s+t\b', r'\1_t', text)  # 时间下标
        text = re.sub(r'\b([a-zA-Z])\s+([0-9]+)\b', r'\1_{\2}', text)  # 数字下标

        # 处理上标
        text = re.sub(r'\^([0-9]+)', r'^{\1}', text)
        text = re.sub(r'\^([a-zA-Z]+)', r'^{\1}', text)
        text = re.sub(r'\^(-[0-9]+)', r'^{\1}', text)

        return text

    def process_functions_and_operators(self, text: str) -> str:
        """处理函数调用和数学运算符"""
        # 函数调用格式化: "f (x)" -> "f(x)"
        text = re.sub(r'([a-zA-Z])\s*\(\s*([^)]+)\s*\)', r'\1(\2)', text)

        # 特殊函数调用: "V (q t )" -> "V(q_t)"
        text = re.sub(r'([A-Z])\s*\(\s*([a-z])\s+([a-z0-9])\s*\)', r'\1(\2_{\3})', text)

        # 处理导数符号
        text = re.sub(r'd([a-zA-Z])', r'd\1', text)

        # 处理微分间距
        text = re.sub(r'\bdt\b', r'\\, dt', text)
        text = re.sub(r'\bdW\b', r'\\, dW', text)
        text = re.sub(r'\bdx\b', r'\\, dx', text)
        text = re.sub(r'\bdy\b', r'\\, dy', text)
        text = re.sub(r'\bdz\b', r'\\, dz', text)

        return text

    def process_special_expressions(self, text: str) -> str:
        """处理特殊的数学表达式"""
        # 平方根表达式
        text = re.sub(r'2\\gamma\\beta\s*\^{-1}', r'\\sqrt{2\\gamma\\beta^{-1}}', text)
        text = re.sub(r'\\sqrt\s*([a-zA-Z0-9\\{}^_-]+)', r'\\sqrt{\1}', text)

        # 分数表达式
        text = re.sub(r'([0-9]+)/([0-9]+)', r'\\frac{\1}{\2}', text)

        # 矩阵和向量
        text = re.sub(r'\\mathbf\{([a-zA-Z])\}', r'\\mathbf{\1}', text)

        # 处理求和、积分等
        text = re.sub(r'\\sum_\{([^}]+)\}\^\{([^}]+)\}', r'\\sum_{\1}^{\2}', text)
        text = re.sub(r'\\int_\{([^}]+)\}\^\{([^}]+)\}', r'\\int_{\1}^{\2}', text)

        return text

    def format_equation_spacing(self, text: str) -> str:
        """格式化方程式间距"""
        # 逗号分隔的方程式
        text = re.sub(r',\s+', r', \\quad ', text)

        # 等号周围的间距
        text = re.sub(r'\s*=\s*', r' = ', text)

        # 加减号周围的间距
        text = re.sub(r'\s*\+\s*', r' + ', text)
        text = re.sub(r'\s*-\s*', r' - ', text)

        # 移除多余的空格
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()

        return text

    def convert_equation_to_latex(self, raw_str: str, eq_num: Optional[str] = None,
                                 equation_type: str = "equation") -> str:
        """
        将原始方程式字符串转换为LaTeX格式

        Args:
            raw_str: 原始方程式字符串
            eq_num: 方程式编号
            equation_type: 方程式类型 ("equation", "align", "cases", "inline")
        """
        text = raw_str

        # 步骤1: 转换Unicode字符
        text = self.convert_unicode_to_latex(text)

        # 步骤2: 处理下标和上标
        text = self.process_subscripts_and_superscripts(text)

        # 步骤3: 处理函数和运算符
        text = self.process_functions_and_operators(text)

        # 步骤4: 处理特殊表达式
        text = self.process_special_expressions(text)

        # 步骤5: 格式化间距
        text = self.format_equation_spacing(text)

        # 移除末尾的句号
        text = re.sub(r'\s*\.\s*$', '', text)

        # 生成LaTeX方程式
        return self.generate_latex_equation(text, eq_num, equation_type)

    def generate_latex_equation(self, text: str, eq_num: Optional[str] = None,
                               equation_type: str = "equation") -> str:
        """生成LaTeX方程式环境"""
        label = f"\\label{{eq:{eq_num.strip('()')}}}" if eq_num else ""

        if equation_type == "inline":
            return f"${text}$"
        elif equation_type == "align":
            # 分割多个方程式
            equations = text.split(', \\quad ')
            aligned_eqs = ' \\\\\n'.join([f"{eq.strip()}" for eq in equations])
            return f"\\begin{{align}}\n{aligned_eqs}\n{label}\n\\end{{align}}"
        elif equation_type == "cases":
            equations = text.split(', \\quad ')
            case_eqs = ' \\\\\n'.join([f"{eq.strip()}" for eq in equations])
            return f"\\begin{{equation}}\n\\begin{{cases}}\n{case_eqs}\n\\end{{cases}}\n{label}\n\\end{{equation}}"
        else:  # equation
            return f"\\begin{{equation}}\n{text}\n{label}\n\\end{{equation}}"

    def process_json_equation(self, eq_data: Dict[str, Any]) -> str:
        """处理JSON格式的方程式数据"""
        raw_str = eq_data.get('raw_str', '')
        eq_num = eq_data.get('eq_num', '')

        return self.convert_equation_to_latex(raw_str, eq_num)

def main():
    """测试通用方程式转换器"""
    print("🧮 通用JSON到LaTeX方程式转换器")
    print("=" * 50)

    # 创建转换器实例
    converter = UniversalEquationConverter()

    # 测试方程式数据
    test_equations = [
        {
            "name": "原始示例方程式",
            "raw_str": "dq t = M \u22121 p t dt, dp t = \u2212\u2207 q V (q t ) dt \u2212 \u03b3M \u22121 p t dt + 2\u03b3\u03b2 \u22121 dW t .",
            "eq_num": "(4)"
        },
        {
            "name": "简单代数方程",
            "raw_str": "x\u00b2 + y\u00b2 = z\u00b2",
            "eq_num": "(1)"
        },
        {
            "name": "积分方程",
            "raw_str": "\u222b\u2080\u00b9 f(x) dx = F(1) - F(0)",
            "eq_num": "(2)"
        },
        {
            "name": "求和公式",
            "raw_str": "\u2211\u1d62\u2080\u207f \u03b1\u1d62 = \u03b1\u2080 + \u03b1\u2081 + ... + \u03b1\u207f",
            "eq_num": "(3)"
        }
    ]

    for i, eq_data in enumerate(test_equations, 1):
        print(f"\n📐 测试 {i}: {eq_data['name']}")
        print(f"   原始: {eq_data['raw_str']}")
        print(f"   编号: {eq_data['eq_num']}")

        # 转换为不同格式
        formats = ["equation", "align", "cases"]
        for fmt in formats:
            latex_result = converter.convert_equation_to_latex(
                eq_data['raw_str'],
                eq_data['eq_num'],
                equation_type=fmt
            )
            print(f"\n   📝 {fmt.upper()} 格式:")
            # 只显示方程式内容的第一行
            first_line = latex_result.split('\n')[1] if '\n' in latex_result else latex_result
            print(f"      {first_line}")

    # 详细测试原始示例
    print(f"\n" + "="*50)
    print("📄 详细测试原始示例方程式")

    equation_data = test_equations[0]
    latex_result = converter.convert_equation_to_latex(
        equation_data['raw_str'],
        equation_data['eq_num']
    )
    
    print("\n📝 转换后的LaTeX:")
    print(latex_result)
    
    # 保存为文件
    with open("converted_equation.tex", "w", encoding="utf-8") as f:
        # 创建完整的LaTeX文档
        full_doc = f"""\\documentclass[12pt,a4paper]{{article}}
\\usepackage[utf8]{{inputenc}}
\\usepackage[T1]{{fontenc}}
\\usepackage{{amsmath}}
\\usepackage{{amssymb}}
\\usepackage{{amsfonts}}

\\begin{{document}}

\\title{{转换的方程式}}
\\maketitle

这是转换后的方程式：

{latex_result}

\\end{{document}}"""
        f.write(full_doc)
    
    print(f"\n✅ 完整LaTeX文档已保存为: converted_equation.tex")
    print(f"\n💡 编译命令: pdflatex converted_equation.tex")
    
    # 显示纯方程式内容（不包括begin/end）
    equation_content = latex_result.split('\n')[1]
    print(f"\n📋 纯方程式内容:")
    print(f"   {equation_content}")

if __name__ == "__main__":
    main()
