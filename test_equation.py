#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试方程式转换功能
"""

from json_to_latex_converter import JSONToLaTeXConverter

def test_equation_conversion():
    """测试特定方程式的转换"""
    print("🧮 测试方程式转换")
    print("=" * 50)
    
    converter = JSONToLaTeXConverter()
    
    # 测试方程式
    equation_data = {
        "start": 0,
        "end": 8,
        "text": "EQUATION",
        "ref_id": "EQREF",
        "raw_str": "dq t = M \u22121 p t dt, dp t = \u2212\u2207 q V (q t ) dt \u2212 \u03b3M \u22121 p t dt + 2\u03b3\u03b2 \u22121 dW t .",
        "eq_num": "(4)"
    }
    
    print("📄 原始方程式数据:")
    print(f"   raw_str: {equation_data['raw_str']}")
    print(f"   eq_num: {equation_data['eq_num']}")
    
    # 转换方程式
    latex_equation = converter.format_equation(
        equation_data['raw_str'], 
        equation_data['eq_num']
    )
    
    print("\n📝 转换后的LaTeX:")
    print(latex_equation)
    
    # 创建完整的LaTeX文档来测试
    full_document = f"""\\documentclass[12pt,a4paper]{{article}}
\\usepackage[utf8]{{inputenc}}
\\usepackage[T1]{{fontenc}}
\\usepackage{{amsmath}}
\\usepackage{{amssymb}}
\\usepackage{{amsfonts}}

\\begin{{document}}

\\title{{方程式测试}}
\\maketitle

这是一个测试方程式：

{latex_equation}

\\end{{document}}"""
    
    # 保存测试文档
    with open("equation_test.tex", "w", encoding="utf-8") as f:
        f.write(full_document)
    
    print(f"\n✅ 完整测试文档已保存为: equation_test.tex")
    
    # 分析转换结果
    print("\n🔍 转换分析:")
    conversions = [
        ("下标处理", "q t" in equation_data['raw_str'], "q_t" in latex_equation),
        ("上标处理", "\u22121" in equation_data['raw_str'], "^{-1}" in latex_equation),
        ("希腊字母γ", "\u03b3" in equation_data['raw_str'], "\\gamma" in latex_equation),
        ("希腊字母β", "\u03b2" in equation_data['raw_str'], "\\beta" in latex_equation),
        ("nabla算子", "\u2207" in equation_data['raw_str'], "\\nabla" in latex_equation),
        ("方程式编号", equation_data['eq_num'], "\\label{eq:4}" in latex_equation),
        ("平方根", "2γβ⁻¹" in equation_data['raw_str'], "\\sqrt{" in latex_equation),
    ]
    
    for desc, has_input, has_output in conversions:
        if has_input:
            status = "✅" if has_output else "❌"
            print(f"   {status} {desc}")
    
    print(f"\n💡 编译建议:")
    print(f"   pdflatex equation_test.tex")

def test_multiple_equations():
    """测试多个方程式的转换"""
    print("\n🔢 测试多个方程式")
    print("-" * 30)
    
    converter = JSONToLaTeXConverter()
    
    test_equations = [
        {
            "name": "简单方程",
            "raw_str": "x = y + z",
            "eq_num": "(1)"
        },
        {
            "name": "希腊字母",
            "raw_str": "\u03b1 + \u03b2 = \u03b3",
            "eq_num": "(2)"
        },
        {
            "name": "复杂微分方程",
            "raw_str": "dX t = f(X t, Y t) dt + \u03c3 dW t",
            "eq_num": "(3)"
        }
    ]
    
    for eq in test_equations:
        print(f"\n📐 {eq['name']}:")
        print(f"   输入: {eq['raw_str']}")
        result = converter.format_equation(eq['raw_str'], eq['eq_num'])
        # 只显示方程式内容，不包括begin/end
        equation_content = result.split('\n')[1]
        print(f"   输出: {equation_content}")

if __name__ == "__main__":
    test_equation_conversion()
    test_multiple_equations()
