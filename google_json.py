import json
import sys
import re
import os

def escape_latex(text):
    """
    Escapes special LaTeX characters in a string, handling basic cases.
    Assumes document uses UTF-8, so accented characters in input JSON
    (like \u00c9) should pass through directly to the output .tex file.
    """
    if not isinstance(text, str):
        return str(text) # Handle non-string input gracefully

    latex_chars = {
        '&': '\\&', '%': '\\%', '$': '\\$', '#': '\\#', '_': '\\_',
        '{': '\\{', '}': '\\}', '~': '\\textasciitilde{}',
        '^': '\\textasciicircum{}', '\\': '\\textbackslash{}',
        '<': '\\textless{}', '>': '\\textgreater{}',
        '|': '\\textbar{}',
        # Quotes: convert straight quotes to LaTeX smart quotes or neutral quotes
        "'": "'",   # Standard apostrophe
        '"': "''",  # Standard double quote (often renders as smart quotes by default)
        # Hyphens: Convert multiple hyphens to emdash or endash
        '---': '---', # Emdash
        '--': '--',   # Endash
    }
    # Build a regex pattern for all characters to escape, sorted by length descending
    # to handle multi-character sequences like '---' before '-'
    pattern = '|'.join(re.escape(key) for key in sorted(latex_chars.keys(), key=len, reverse=True))

    # Use a replacement function to look up the correct LaTeX escape
    return re.sub(pattern, lambda match: latex_chars[match.group(0)], text)

def format_author(author_data):
    """Formats a single author dictionary into a string for LaTeX authblk."""
    # This JSON structure uses 'first', 'middle', 'last'
    first_name = author_data.get('first', '').strip()
    middle_names = " ".join(author_data.get('middle', [])).strip()
    last_name = author_data.get('last', '').strip()
    suffix = author_data.get('suffix', '').strip()

    name_parts = []
    if first_name:
        name_parts.append(first_name)
    if middle_names:
        name_parts.append(middle_names)
    if last_name:
        name_parts.append(last_name)
    if suffix:
         name_parts.append(suffix) # Suffix like Jr., III etc.

    full_name = " ".join(name_parts).strip()

    # Escape LaTeX special characters in the name
    return escape_latex(full_name)

def format_reference(ref_id, ref_data):
    """
    Formats a single reference dictionary into a LaTeX \bibitem string.
    Uses the provided ref_id as the label key.
    """
    # Use the raw_text if available, otherwise try to build from parsed fields.
    # The 'bib_entries' structure is a bit different from standard GROBID TEI biblio.
    # Let's prioritize raw_text as it's often the most complete.
    raw_text = ref_data.get('raw_text', '').strip()

    if raw_text:
         # Just use the raw text after the bibitem key
         content = escape_latex(raw_text)
    else:
        # Fallback: Try to build from parsed fields in the JSON structure
        # This is a simplified attempt and might not cover all cases
        parts = []
        authors_list = ref_data.get('authors', []) # List of author dicts
        if authors_list:
            author_names = []
            for author in authors_list:
                 # These author dicts seem simpler than header authors
                 auth_parts = []
                 if author.get('first'): auth_parts.append(author['first'])
                 if author.get('middle'): auth_parts.extend(author['middle'])
                 if author.get('last'): auth_parts.append(author['last'])
                 name = " ".join(auth_parts).strip()
                 if name: author_names.append(escape_latex(name))

            if author_names:
                parts.append(", ".join(author_names))


        title = ref_data.get('title', '').strip()
        if title:
            # Basic heuristic for title style (might need refinement)
            # Assume articles/chapters need quotes, books/journals italics.
            # This JSON doesn't clearly tag type, so guess based on venue presence.
            if ref_data.get('venue') or ref_data.get('publisher'):
                 parts.append(f"``{escape_latex(title)}''") # Assume article/chapter
            else:
                 parts.append(f"\\textit{{{escape_latex(title)}}}") # Assume book/report/thesis etc.

        # Journal/Book/Conference info
        venue = ref_data.get('venue', '').strip()
        if venue:
            parts.append(f"\\textit{{{escape_latex(venue)}}}")

        volume = ref_data.get('volume', '').strip()
        issue = ref_data.get('issue', '').strip()
        if volume or issue:
            vol_issue = []
            if volume: vol_issue.append(escape_latex(volume))
            if issue: vol_issue.append(f"({escape_latex(issue)})")
            if vol_issue: parts.append(" ".join(vol_issue))

        pages = ref_data.get('pages', '').strip()
        if pages:
            parts.append(f"pp. {escape_latex(pages)}")

        year = ref_data.get('year', '').strip()
        if year:
             parts.append(f"({escape_latex(year)})")

        # Combine parsed parts
        content = ", ".join(parts) + "." if parts else "Unknown reference details."

    # Ensure ref_id is valid for LaTeX label
    valid_ref_key = re.sub(r'[^a-zA-Z0-9:_-]', '', ref_id)
    if not valid_ref_key:
         valid_ref_key = f"ref_{abs(hash(ref_id))}" # Fallback if ref_id is invalid chars

    return f"\\bibitem{{{escape_latex(valid_ref_key)}}}\n{content}\n"


def process_content_blocks(blocks, ref_entries, bib_entries):
    """Processes a list of content blocks (e.g., body_text, back_matter)."""
    block_latex = ""
    if not blocks:
        return block_latex

    current_section_title = None
    current_sec_num = None

    for i, block in enumerate(blocks):
        section_title = block.get('section', '').strip()
        sec_num = block.get('sec_num', '') # Keep as is to check for change

        # Check if a new section heading is needed
        is_new_section = False
        if section_title and (section_title != current_section_title or sec_num != current_sec_num):
             is_new_section = True
             current_section_title = section_title
             current_sec_num = sec_num

        if is_new_section:
             escaped_section_title = escape_latex(section_title)
             if sec_num:
                 # Determine section level based on sec_num structure
                 # 1 -> section, 1.1 -> subsection, 1.1.1 -> subsubsection etc.
                 level = sec_num.count('.') + 1
                 if level == 1:
                     block_latex += f"\\section{{{escaped_section_title}}}\n"
                 elif level == 2:
                      block_latex += f"\\subsection{{{escaped_section_title}}}\n"
                 elif level == 3:
                      block_latex += f"\\subsubsection{{{escaped_section_title}}}\n"
                 else:
                      block_latex += f"\\paragraph{{{escaped_section_title}.}}\n" # Or just print text
             else:
                 # Unnumbered section if no sec_num
                 block_latex += f"\\section*{{{escaped_section_title}}}\n"

        # Process the text content within the block
        text_content = block.get('text', '')
        cite_spans = block.get('cite_spans', [])
        ref_spans = block.get('ref_spans', []) # These might link to ref_entries or bib_entries
        eq_spans = block.get('eq_spans', [])

        processed_text = ""
        last_offset = 0

        # Collect all inline elements and sort by start offset
        inline_elements = []
        for span in cite_spans:
             span['type'] = 'cite'
             inline_elements.append(span)
        for span in ref_spans:
             span['type'] = 'ref' # May refer to figures/tables (ref_entries) or bib (bib_entries)
             inline_elements.append(span)
        for span in eq_spans:
             span['type'] = 'eq' # Display equations based on JSON structure
             inline_elements.append(span)

        # Sort by start offset
        inline_elements.sort(key=lambda x: x.get('start', 0))

        # Process text chunks and insert formatted inline elements
        for element in inline_elements:
            start = element.get('start', 0)
            end = element.get('end', 0) # End of the text span corresponding to the element

            # Add text before this inline element
            processed_text += escape_latex(text_content[last_offset:start])

            # Process the inline element based on its type
            element_type = element.get('type')
            element_ref_id = element.get('ref_id') # Used for cite/ref
            element_raw_str = element.get('raw_str', '') # Used for equations
            element_eq_num = element.get('eq_num', '') # Used for equation labels

            if element_type == 'cite' and element_ref_id:
                 # Check if ref_id exists in bib_entries before creating cite
                 if element_ref_id in bib_entries:
                     processed_text += f"\\cite{{{escape_latex(element_ref_id)}}}"
                 else:
                     # Fallback if ref_id not found in bib_entries
                     processed_text += f"[??{escape_latex(element_ref_id)}??]" # Placeholder or warning
                     # Alternatively, just output the original text span
                     # processed_text += escape_latex(text_content[start:end])
                     print(f"Warning: cite ref_id '{element_ref_id}' not found in bib_entries.")

            elif element_type == 'ref' and element_ref_id:
                 # In this JSON, ref_spans often point to BIBREF.
                 # If they point to ref_entries keys (FIGREF, TABREF etc.), use \ref.
                 # If they point to BIBREF, they are likely meant as citations.
                 # Let's check if the key exists in ref_entries or bib_entries.
                 if element_ref_id in ref_entries:
                     # Assuming ref_id maps to a figure/table label
                     processed_text += f"\\ref{{{escape_latex(element_ref_id)}}}"
                 elif element_ref_id in bib_entries:
                      # Treat ref_span pointing to BIBREF as a citation
                     processed_text += f"\\cite{{{escape_latex(element_ref_id)}}}"
                 else:
                     # Fallback if ref_id not found in either
                     processed_text += f"[??{escape_latex(element_ref_id)}??]" # Placeholder or warning
                     # processed_text += escape_latex(text_content[start:end]) # Output original text
                     print(f"Warning: ref_span ref_id '{element_ref_id}' not found in ref_entries or bib_entries.")

            elif element_type == 'eq' and element_raw_str:
                 # Format as a display equation block
                 # Note: This inserts display math *inline* within the paragraph processing.
                 # A cleaner approach might break the paragraph, but this follows GROBID structure.
                 # Using \[ \] for unnumbered, equation for numbered.
                 eq_label = ""
                 if element_eq_num:
                     # Create a valid label from eq_num
                     valid_eq_label = re.sub(r'[^a-zA-Z0-9:_]', '', element_eq_num)
                     if valid_eq_label:
                          eq_label = f"\\label{{eq:{escape_latex(valid_eq_label)}}}"
                          processed_text += f"\n\\[ {escape_latex(element_raw_str)} {eq_label} \\]\n" # Unnumbered with label
                     else:
                          processed_text += f"\n\\[ {escape_latex(element_raw_str)} \\]\n" # Unnumbered
                 else:
                      processed_text += f"\n\\[ {escape_latex(element_raw_str)} \\]\n" # Unnumbered

                 # Update last_offset to the end of the raw_str span, not the original "EQUATION" text span
                 # This is tricky. GROBID's offsets are usually for the "EQUATION" placeholder.
                 # Let's rely on the placeholder span start/end for skipping.
                 pass # last_offset will be updated below based on element['end']

            # Important: Update the last_offset to the end of the *original text span*
            # covered by the inline element, so we don't re-process it.
            last_offset = end

        # Add any remaining text after the last inline element
        processed_text += escape_latex(text_content[last_offset:])

        # Add the processed text as a paragraph block
        block_latex += processed_text.strip() + "\n\n" # Use two newlines for a paragraph break

    return block_latex


def grobid_json_to_latex(json_data):
    """Converts a GROBID-like JSON dictionary into a LaTeX string."""
    latex_string = ""

    # --- Preamble ---
    latex_string += "\\documentclass{article}\n"
    latex_string += "\\usepackage[utf8]{inputenc}\n" # Required for special characters like accents
    latex_string += "\\usepackage[T1]{fontenc}\n"    # Recommended font encoding
    latex_string += "\\usepackage{graphicx}\n"       # For figures (even if not auto-placed, needed for refs)
    latex_string += "\\usepackage{hyperref}\n"       # For links, citations, and references
    latex_string += "\\usepackage{authblk}\n"        # For author list with affiliations
    latex_string += "\\usepackage{amsmath}\n"        # For mathematical equations
    # latex_string += "\\usepackage{booktabs}\n"     # For better tables (if manually added)
    latex_string += "\\usepackage{geometry}\n"
    latex_string += "\\geometry{a4paper, margin=1in}\n" # Example margins, adjust as needed
    latex_string += "\n"

    # --- Metadata ---
    title = json_data.get('title', 'Untitled Document')
    latex_string += f"\\title{{{escape_latex(title)}}}\n"

    authors = json_data.get('authors', [])
    if authors:
        # authblk handles authors like \author{Name1 \and Name2}
        # or \author{Name1}\affil{Affil1}\author{Name2}\affil{Affil2}
        # This simplified version just lists names separated by \and.
        author_names = [format_author(author) for author in authors]
        # Filter out empty names
        author_names = [name for name in author_names if name]
        if author_names:
             latex_string += "\\author{%s}\n" % " \\and ".join(author_names)
        # Detailed affiliation handling requires more complex parsing of 'affiliation'

    # You could try to parse a date from header or other fields if available
    latex_string += "\\date{}" # No date by default

    latex_string += "\n\\begin{document}\n"

    latex_string += "\\maketitle\n"

    # --- Abstract ---
    # The JSON has abstract text directly under the root and also in pdf_parse.abstract
    # Let's use the root level abstract if available, otherwise try pdf_parse.abstract
    abstract_text = json_data.get('abstract', '').strip()
    if not abstract_text:
         # Check pdf_parse.abstract, which is a list of blocks
         abstract_blocks = json_data.get('pdf_parse', {}).get('abstract', [])
         # Concatenate text from abstract blocks
         abstract_text = "\n\n".join(block.get('text', '').strip() for block in abstract_blocks if block.get('text'))


    if abstract_text:
        latex_string += "\\begin{abstract}\n"
        # Pass abstract blocks through the content processor to handle citations etc.
        # Need to simulate block structure for the abstract text
        # Let's just escape the concatenated text for simplicity for the abstract
        latex_string += escape_latex(abstract_text) + "\n"
        latex_string += "\\end{abstract}\n\n"

    # --- Body ---
    # Process body_text blocks, using the content processor
    body_blocks = json_data.get('pdf_parse', {}).get('body_text', [])
    ref_entries = json_data.get('pdf_parse', {}).get('ref_entries', {}) # Pass ref_entries for \ref lookup
    bib_entries = json_data.get('pdf_parse', {}).get('bib_entries', {}) # Pass bib_entries for \cite lookup


    latex_string += process_content_blocks(body_blocks, ref_entries, bib_entries)


    # --- Back Matter (Optional) ---
    back_matter_blocks = json_data.get('pdf_parse', {}).get('back_matter', [])
    if back_matter_blocks:
        # Add a break or header before back matter if it's distinct
        # The provided JSON's back_matter contains presentation list under 'funding' section?
        # Let's process it with the same logic, section headers included.
        latex_string += "% --- Back Matter ---\n"
        latex_string += process_content_blocks(back_matter_blocks, ref_entries, bib_entries)


    # --- References ---
    # GROBID usually puts references in back_matter.
    # Your JSON has parsed bib_entries separately.
    # Let's generate the bibliography from bib_entries.
    if bib_entries:
        latex_string += "\\section*{References}\n" # Use unnumbered section for bibliography

        # Use thebibliography environment with bibitem for compatibility with \cite
        # The argument specifies the widest label, e.g., {99} for up to 99 items.
        # Count the number of references to get a better estimate, but 99 is common.
        widest_label_num = max(len(str(len(bib_entries))), 2) # e.g., 10 for <=99, 100 for <=999
        widest_label = '9' * widest_label_num

        latex_string += f"\\begin{{thebibliography}}{{{widest_label}}}\n"
        # Sort bib_entries by ref_id key for consistent output order
        for ref_id, ref_data in sorted(bib_entries.items()):
            latex_string += format_reference(ref_id, ref_data)
        latex_string += "\\end{thebibliography}\n"


    # --- End Document ---
    latex_string += "\n\\end{document}\n"

    return latex_string

# --- Main execution block ---
if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python grobid_json_to_latex.py <input_grobid.json> <output.tex>")
        sys.exit(1)

    input_json_file = sys.argv[1]
    output_tex_file = sys.argv[2]

    try:
        # Read the JSON file
        # Using 'utf-8' as it's standard and handles characters like accents
        with open(input_json_file, 'r', encoding='utf-8') as f:
            grobid_data = json.load(f)

        # Convert JSON to LaTeX string
        latex_output = grobid_json_to_latex(grobid_data)

        # Write the LaTeX string to a .tex file
        with open(output_tex_file, 'w', encoding='utf-8') as f:
            f.write(latex_output)

        print(f"Successfully converted '{input_json_file}' to '{output_tex_file}'")
        print("Note: You may need to manually adjust the generated .tex file for detailed formatting, figures, tables, and complex structures.")
        print("Compile the .tex file using a LaTeX distribution (e.g., pdflatex).")

    except FileNotFoundError:
        print(f"Error: Input file not found at '{input_json_file}'")
        sys.exit(1)
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from '{input_json_file}'. Make sure it's valid JSON.")
        sys.exit(1)
    except Exception as e:
        print(f"An error occurred during conversion: {e}")
        # Optional: Print traceback for debugging
        # import traceback
        # traceback.print_exc()
        sys.exit(1)