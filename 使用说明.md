# JSON到LaTeX转换器使用说明

这个程序可以将JSON格式的学术论文（特别是GROBID解析的PDF文件）转换为LaTeX格式。

## 功能特点

- ✅ 支持法语重音字符的正确转换
- ✅ 处理数学符号和方程式
- ✅ 自动生成文档结构（标题、作者、摘要、章节）
- ✅ 支持Unicode字符到LaTeX命令的转换
- ✅ 优化的法语文档格式

## 使用方法

### 1. 命令行使用

```bash
# 基本用法
python json_to_latex_converter.py input.json

# 指定输出文件
python json_to_latex_converter.py input.json -o output.tex

# 不包含元数据（标题、作者等）
python json_to_latex_converter.py input.json --no-metadata

# 指定文档类
python json_to_latex_converter.py input.json --document-class report
```

### 2. Python脚本中使用

```python
from json_to_latex_converter import JSONToLaTeXConverter

# 创建转换器
converter = JSONToLaTeXConverter()

# 转换文件
converter.convert_file(
    input_file="input.json",
    output_file="output.tex",
    include_metadata=True,
    document_class="article"
)
```

## 示例

转换您的JSON文件：

```bash
python json_to_latex_converter.py "output_dir/mcf_analytical_presentation.json" -o "output_dir/mcf_analytical_presentation.tex"
```

## 生成的LaTeX文档特点

- 使用`article`文档类，A4纸张，12pt字体
- 包含法语babel包支持
- 自动处理法语重音字符
- 包含数学包（amsmath, amssymb等）
- 2.5cm页边距
- UTF-8编码支持

## 编译LaTeX文档

生成LaTeX文件后，您可以使用以下命令编译：

```bash
# 使用pdflatex编译
pdflatex mcf_analytical_presentation.tex

# 如果有引用，可能需要多次编译
pdflatex mcf_analytical_presentation.tex
bibtex mcf_analytical_presentation
pdflatex mcf_analytical_presentation.tex
pdflatex mcf_analytical_presentation.tex
```

## 支持的字符转换

### 法语重音字符
- à, á, â, ä, è, é, ê, ë, ì, í, î, ï, ò, ó, ô, ö, ù, ú, û, ü
- ç, ñ 和对应的大写字母

### 数学符号
- 希腊字母：α, β, γ, δ, ε, θ, λ, μ, π, σ, ω 等
- 数学运算符：∂, ∇, ±, ×, ÷, ≠, ≤, ≥, ≈, ∞ 等
- 集合符号：∈, ∉, ⊂, ⊃, ∩, ∪, ∅ 等
- 箭头：→, ←, ↔, ⇒, ⇐, ⇔ 等

## 注意事项

1. 确保输入的JSON文件格式正确
2. 生成的LaTeX文件使用UTF-8编码
3. 如果包含复杂的数学公式，可能需要手动调整
4. 建议在编译前检查生成的LaTeX代码

## 故障排除

如果遇到编译错误：
1. 检查LaTeX发行版是否完整安装
2. 确保安装了法语babel包
3. 检查特殊字符是否正确转换
4. 查看LaTeX日志文件获取详细错误信息
