<!-- Generated by SpecStory -->

# pylatex_converter.py: error: unrecognized argument (2025-06-24 19:10:11)

_**User**_

pylatex_converter.py: error: unrecognized arguments: D:\workspace\french_cv\output_dir\dd.tex


---

_**Assistant**_

I can see the issue. The error suggests that you're trying to run the script with an argument that it doesn't recognize. Looking at the code, the argument parser expects specific arguments but it seems like you're passing a file path that's not being handled correctly.

Let me check the current argument structure and fix it: The issue is that the argument parser is set up to expect:
1. An input JSON file as a positional argument
2. An output file specified with `-o` or `--output`

But it looks like you're trying to pass `D:\workspace\french_cv\output_dir\dd.tex` as a positional argument, which the parser doesn't recognize because it expects a JSON file as input, not a TEX file as output.

Let me fix the argument parser to be more flexible and handle this case better: 
```

```
Now the script will:

1. **Detect common mistakes**: If you pass a `.tex` file as the first argument, it will show a helpful error message explaining the correct usage.

2. **Show help when no arguments**: If you run the script without arguments, it will show the help message.

3. **Better error handling**: More informative error messages.

## Correct Usage Examples:

1. **Convert a JSON file to LaTeX:**
   ```
   python pylatex_converter.py input.json -o output.tex
   ```

2. **Run the test:**
   ```
   python pylatex_converter.py --test
   ```

3. **Show help:**
   ```
   python pylatex_converter.py
   ```

The error you encountered suggests you were trying to run something like:
```
python pylatex_converter.py D:\workspace\french_cv\output_dir\dd.tex
```

Instead, you should run:
```
python pylatex_converter.py input.json -o D:\workspace\french_cv\output_dir\dd.tex
```

Where `input.json` is your GROBID JSON file that you want to convert to LaTeX.

---

