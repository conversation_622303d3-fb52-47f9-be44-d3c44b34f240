#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门的方程式转换器
处理您提供的具体方程式
"""

import re

def convert_equation_to_latex(raw_str, eq_num=None):
    """
    将原始方程式字符串转换为LaTeX格式
    """
    # 原始字符串
    text = raw_str
    
    # 手动处理Unicode字符
    replacements = {
        '\u22121': '^{-1}',  # ⁻¹
        '\u2212': '-',       # −
        '\u2207': '\\nabla', # ∇
        '\u03b3': '\\gamma', # γ
        '\u03b2': '\\beta',  # β
    }
    
    for unicode_char, latex_cmd in replacements.items():
        text = text.replace(unicode_char, latex_cmd)
    
    # 处理变量下标 (如 "q t" -> "q_t")
    text = re.sub(r'\b([a-zA-Z])\s+t\b', r'\1_t', text)
    
    # 处理函数调用 (如 "V (q t )" -> "V(q_t)")
    text = re.sub(r'([A-Z])\s*\(\s*([a-z])\s+([a-z])\s*\)', r'\1(\2_\3)', text)
    
    # 处理 M^{-1} 的情况
    text = re.sub(r'M\s*\^{-1}', r'M^{-1}', text)
    text = re.sub(r'\\gamma\s*M\s*\^{-1}', r'\\gamma M^{-1}', text)
    
    # 处理平方根表达式
    text = re.sub(r'2\\gamma\\beta\s*\^{-1}', r'\\sqrt{2\\gamma\\beta^{-1}}', text)
    
    # 添加适当的数学间距
    text = re.sub(r'\bdt\b', r'\\, dt', text)
    text = re.sub(r'\bdW\b', r'\\, dW', text)
    
    # 处理逗号分隔的方程式
    text = re.sub(r',\s+', r', \\quad ', text)
    
    # 移除末尾的句号
    text = re.sub(r'\s*\.\s*$', '', text)
    
    # 生成完整的LaTeX方程式
    if eq_num:
        eq_label = eq_num.strip('()')
        return f"\\begin{{equation}}\n{text}\n\\label{{eq:{eq_label}}}\n\\end{{equation}}"
    else:
        return f"\\begin{{equation}}\n{text}\n\\end{{equation}}"

def main():
    """测试方程式转换"""
    print("🧮 专门的方程式转换器")
    print("=" * 40)
    
    # 您提供的方程式数据
    equation_data = {
        "raw_str": "dq t = M \u22121 p t dt, dp t = \u2212\u2207 q V (q t ) dt \u2212 \u03b3M \u22121 p t dt + 2\u03b3\u03b2 \u22121 dW t .",
        "eq_num": "(4)"
    }
    
    print("📄 原始方程式:")
    print(f"   {equation_data['raw_str']}")
    print(f"   编号: {equation_data['eq_num']}")
    
    # 转换
    latex_result = convert_equation_to_latex(
        equation_data['raw_str'], 
        equation_data['eq_num']
    )
    
    print("\n📝 转换后的LaTeX:")
    print(latex_result)
    
    # 保存为文件
    with open("converted_equation.tex", "w", encoding="utf-8") as f:
        # 创建完整的LaTeX文档
        full_doc = f"""\\documentclass[12pt,a4paper]{{article}}
\\usepackage[utf8]{{inputenc}}
\\usepackage[T1]{{fontenc}}
\\usepackage{{amsmath}}
\\usepackage{{amssymb}}
\\usepackage{{amsfonts}}

\\begin{{document}}

\\title{{转换的方程式}}
\\maketitle

这是转换后的方程式：

{latex_result}

\\end{{document}}"""
        f.write(full_doc)
    
    print(f"\n✅ 完整LaTeX文档已保存为: converted_equation.tex")
    print(f"\n💡 编译命令: pdflatex converted_equation.tex")
    
    # 显示纯方程式内容（不包括begin/end）
    equation_content = latex_result.split('\n')[1]
    print(f"\n📋 纯方程式内容:")
    print(f"   {equation_content}")

if __name__ == "__main__":
    main()
