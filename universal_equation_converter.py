#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用JSON到LaTeX方程式转换器
简单易用的接口，支持各种数学表达式
"""

import re
import json
from typing import Optional, Dict, Any

def convert_equation_to_latex(raw_str: str, eq_num: Optional[str] = None, 
                             equation_type: str = "equation") -> str:
    """
    通用方程式转换函数
    
    Args:
        raw_str: 原始方程式字符串（可能包含Unicode字符）
        eq_num: 方程式编号，如 "(1)", "(2)" 等
        equation_type: 输出格式类型
            - "equation": 标准方程式环境
            - "align": 对齐方程式环境（适合多行）
            - "cases": 分段函数格式
            - "inline": 行内数学模式
    
    Returns:
        str: 格式化的LaTeX方程式代码
    """
    
    # Unicode到LaTeX的映射表
    unicode_map = {
        # 希腊字母
        '\u03b1': r'\alpha', '\u03b2': r'\beta', '\u03b3': r'\gamma', '\u03b4': r'\delta',
        '\u03b5': r'\epsilon', '\u03b6': r'\zeta', '\u03b7': r'\eta', '\u03b8': r'\theta',
        '\u03b9': r'\iota', '\u03ba': r'\kappa', '\u03bb': r'\lambda', '\u03bc': r'\mu',
        '\u03bd': r'\nu', '\u03be': r'\xi', '\u03c0': r'\pi', '\u03c1': r'\rho',
        '\u03c3': r'\sigma', '\u03c4': r'\tau', '\u03c5': r'\upsilon', '\u03c6': r'\phi',
        '\u03c7': r'\chi', '\u03c8': r'\psi', '\u03c9': r'\omega', '\u03d5': r'\phi',
        '\u0393': r'\Gamma', '\u0394': r'\Delta', '\u0398': r'\Theta', '\u039b': r'\Lambda',
        '\u039e': r'\Xi', '\u03a0': r'\Pi', '\u03a3': r'\Sigma', '\u03a5': r'\Upsilon',
        '\u03a6': r'\Phi', '\u03a8': r'\Psi', '\u03a9': r'\Omega',
        
        # 数学运算符
        '\u2202': r'\partial', '\u2207': r'\nabla', '\u2212': r'-', '\u00b1': r'\pm',
        '\u00d7': r'\times', '\u00f7': r'\div', '\u2260': r'\neq', '\u2264': r'\leq',
        '\u2265': r'\geq', '\u2248': r'\approx', '\u2261': r'\equiv', '\u221d': r'\propto',
        '\u221e': r'\infty', '\u2211': r'\sum', '\u220f': r'\prod', '\u222b': r'\int',
        '\u222c': r'\iint', '\u222d': r'\iiint',
        
        # 集合符号
        '\u2208': r'\in', '\u2209': r'\notin', '\u2282': r'\subset', '\u2283': r'\supset',
        '\u2286': r'\subseteq', '\u2287': r'\supseteq', '\u2229': r'\cap', '\u222a': r'\cup',
        '\u2205': r'\emptyset', '\u2200': r'\forall', '\u2203': r'\exists',
        
        # 箭头
        '\u2192': r'\rightarrow', '\u2190': r'\leftarrow', '\u2194': r'\leftrightarrow',
        '\u21d2': r'\Rightarrow', '\u21d0': r'\Leftarrow', '\u21d4': r'\Leftrightarrow',
        
        # 上标数字
        '\u2070': r'^{0}', '\u00b9': r'^{1}', '\u00b2': r'^{2}', '\u00b3': r'^{3}',
        '\u2074': r'^{4}', '\u2075': r'^{5}', '\u2076': r'^{6}', '\u2077': r'^{7}',
        '\u2078': r'^{8}', '\u2079': r'^{9}', '\u207a': r'^{+}', '\u207b': r'^{-}',
        
        # 下标数字
        '\u2080': r'_{0}', '\u2081': r'_{1}', '\u2082': r'_{2}', '\u2083': r'_{3}',
        '\u2084': r'_{4}', '\u2085': r'_{5}', '\u2086': r'_{6}', '\u2087': r'_{7}',
        '\u2088': r'_{8}', '\u2089': r'_{9}',
        
        # 特殊符号
        '\u221a': r'\sqrt', '\u2026': r'\ldots', '\u22ef': r'\cdots',
        '\u2102': r'\mathbb{C}', '\u2115': r'\mathbb{N}', '\u211a': r'\mathbb{Q}',
        '\u211d': r'\mathbb{R}', '\u2124': r'\mathbb{Z}',
    }
    
    text = raw_str
    
    # 步骤1: 处理特殊的上标-1组合
    text = re.sub(r'([a-zA-Z0-9}])\s*⁻¹', r'\1^{-1}', text)
    text = re.sub(r'([a-zA-Z0-9}])\s*\u207b\u00b9', r'\1^{-1}', text)
    
    # 步骤2: 替换Unicode字符
    for unicode_char, latex_cmd in unicode_map.items():
        text = text.replace(unicode_char, latex_cmd)

    # 步骤2.5: 修复希腊字母后跟大写字母的问题
    text = re.sub(r'(\\[a-z]+)([A-Z])', r'\1 \2', text)
    
    # 步骤3: 处理下标和上标
    # 处理特殊的上标情况（如 M −1 应该变成 M^{-1}）
    text = re.sub(r'\b([A-Z])\s+(-\s*1)\b', r'\1^{-1}', text)  # M −1 -> M^{-1}
    text = re.sub(r'\\beta\s+(-\s*1)\b', r'\\beta^{-1}', text)  # β −1 -> β^{-1}

    # 处理时间下标（如 q t, p t, dW t）
    text = re.sub(r'\b([a-zA-Z]+)\s+t\b', r'\1_t', text)  # 时间下标
    text = re.sub(r'\bd([A-Z])\s+t\b', r'd\1_t', text)  # dW t -> dW_t

    # 处理梯度下标（如 ∇ q V -> ∇_q V）
    text = re.sub(r'\\nabla\s+([a-z])\s+([A-Z])', r'\\nabla_{\1} \2', text)

    # 一般下标处理
    text = re.sub(r'\b([a-zA-Z])\s+([a-zA-Z0-9])\b', r'\1_{\2}', text)  # 一般下标
    text = re.sub(r'\^([0-9]+)', r'^{\1}', text)  # 数字上标
    text = re.sub(r'\^([a-zA-Z]+)', r'^{\1}', text)  # 字母上标
    text = re.sub(r'\^(-[0-9]+)', r'^{\1}', text)  # 负数上标
    
    # 步骤4: 处理函数调用
    text = re.sub(r'([a-zA-Z])\s*\(\s*([^)]+)\s*\)', r'\1(\2)', text)  # 一般函数
    text = re.sub(r'([A-Z])\s*\(\s*([a-z])\s+([a-z0-9])\s*\)', r'\1(\2_{\3})', text)  # 特殊函数
    
    # 步骤5: 处理微分符号
    text = re.sub(r'\bdt\b', r'\\, dt', text)
    text = re.sub(r'\bdW\b', r'\\, dW', text)
    text = re.sub(r'\bdx\b', r'\\, dx', text)
    text = re.sub(r'\bdy\b', r'\\, dy', text)
    
    # 步骤6: 处理特殊表达式
    text = re.sub(r'([0-9]+)/([0-9]+)', r'\\frac{\1}{\2}', text)  # 分数
    
    # 步骤7: 格式化间距
    text = re.sub(r',\s+', r', \\quad ', text)  # 逗号分隔
    text = re.sub(r'\s*=\s*', r' = ', text)  # 等号间距
    text = re.sub(r'\s*\+\s*', r' + ', text)  # 加号间距
    # 小心处理减号，避免影响上标中的负号
    text = re.sub(r'(?<!\^{)\s*-\s*(?![0-9]})', r' - ', text)  # 减号间距，但不影响上标
    text = re.sub(r'\s+', ' ', text)  # 多余空格
    # 清理上标中的多余空格
    text = re.sub(r'\^{\s*(-?\s*[0-9]+)\s*}', r'^{\1}', text)  # 清理上标内空格
    text = re.sub(r'\^{\s*-\s*([0-9]+)\s*}', r'^{-\1}', text)  # 修正上标中的负号
    text = text.strip()
    
    # 步骤8: 移除末尾句号
    text = re.sub(r'\s*\.\s*$', '', text)
    
    # 步骤9: 生成LaTeX环境
    label = f"\\label{{eq:{eq_num.strip('()')}}}" if eq_num else ""
    
    if equation_type == "inline":
        return f"${text}$"
    elif equation_type == "align":
        equations = text.split(', \\quad ')
        aligned_eqs = ' \\\\\n'.join([f"{eq.strip()}" for eq in equations])
        return f"\\begin{{align}}\n{aligned_eqs}\n{label}\n\\end{{align}}"
    elif equation_type == "cases":
        equations = text.split(', \\quad ')
        case_eqs = ' \\\\\n'.join([f"{eq.strip()}" for eq in equations])
        return f"\\begin{{equation}}\n\\begin{{cases}}\n{case_eqs}\n\\end{{cases}}\n{label}\n\\end{{equation}}"
    else:  # equation
        return f"\\begin{{equation}}\n{text}\n{label}\n\\end{{equation}}"


def convert_json_equation(eq_data: Dict[str, Any], equation_type: str = "equation") -> str:
    """
    从JSON数据转换方程式
    
    Args:
        eq_data: 包含 'raw_str' 和 'eq_num' 的字典
        equation_type: LaTeX环境类型
    
    Returns:
        str: LaTeX方程式代码
    """
    raw_str = eq_data.get('raw_str', '')
    eq_num = eq_data.get('eq_num', '')
    
    return convert_equation_to_latex(raw_str, eq_num, equation_type)


def batch_convert_equations(equations_list: list, equation_type: str = "equation") -> list:
    """
    批量转换方程式
    
    Args:
        equations_list: 方程式数据列表
        equation_type: LaTeX环境类型
    
    Returns:
        list: 转换后的LaTeX代码列表
    """
    results = []
    for eq_data in equations_list:
        if isinstance(eq_data, dict):
            latex_code = convert_json_equation(eq_data, equation_type)
        elif isinstance(eq_data, str):
            latex_code = convert_equation_to_latex(eq_data, equation_type=equation_type)
        else:
            continue
        results.append(latex_code)
    
    return results


# 使用示例
if __name__ == "__main__":
    # 示例1: 基本使用
    equation = "dq t = M \u22121 p t dt, dp t = \u2212\u2207 q V (q t ) dt \u2212 \u03b3M \u22121 p t dt + 2\u03b3\u03b2 \u22121 dW t ."
    
    print("🧮 通用方程式转换器示例")
    print("=" * 40)
    print(f"原始方程式: {equation}")
    print()
    
    # 不同格式的转换
    formats = ["equation", "align", "cases", "inline"]
    for fmt in formats:
        result = convert_equation_to_latex(equation, "(4)", fmt)
        print(f"📝 {fmt.upper()} 格式:")
        print(result)
        print()
    
    # 示例2: JSON数据转换
    json_data = {
        "raw_str": "\u03b1 + \u03b2 = \u03b3",
        "eq_num": "(1)"
    }
    
    print("📄 JSON数据转换:")
    print(f"输入: {json_data}")
    result = convert_json_equation(json_data)
    print(f"输出:\n{result}")
