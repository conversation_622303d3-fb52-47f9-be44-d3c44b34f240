#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用转换器测试脚本
"""

from universal_equation_converter import (
    convert_equation_to_latex, 
    convert_json_equation, 
    batch_convert_equations
)

def test_basic_conversion():
    """测试基本转换功能"""
    print("🧪 测试基本转换功能")
    print("=" * 40)
    
    test_cases = [
        {
            "name": "原始示例方程式",
            "input": "dq t = M \u22121 p t dt, dp t = \u2212\u2207 q V (q t ) dt \u2212 \u03b3M \u22121 p t dt + 2\u03b3\u03b2 \u22121 dW t .",
            "eq_num": "(4)"
        },
        {
            "name": "简单代数方程",
            "input": "x\u00b2 + y\u00b2 = z\u00b2",
            "eq_num": "(1)"
        },
        {
            "name": "希腊字母方程",
            "input": "\u03b1 + \u03b2 = \u03b3",
            "eq_num": "(2)"
        },
        {
            "name": "积分方程",
            "input": "\u222b\u2080\u00b9 f(x) dx = F(1) - F(0)",
            "eq_num": "(3)"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📐 测试 {i}: {case['name']}")
        print(f"   输入: {case['input']}")
        
        result = convert_equation_to_latex(case['input'], case['eq_num'])
        # 只显示方程式内容
        equation_line = result.split('\n')[1] if '\n' in result else result
        print(f"   输出: {equation_line}")

def test_different_formats():
    """测试不同输出格式"""
    print("\n🎨 测试不同输出格式")
    print("=" * 40)
    
    equation = "dq t = M \u22121 p t dt, dp t = \u2212\u2207 q V (q t ) dt"
    formats = ["equation", "align", "cases", "inline"]
    
    for fmt in formats:
        print(f"\n📝 {fmt.upper()} 格式:")
        result = convert_equation_to_latex(equation, "(1)", fmt)
        if fmt == "inline":
            print(f"   {result}")
        else:
            # 显示第一行内容
            lines = result.split('\n')
            content_line = lines[1] if len(lines) > 1 else result
            print(f"   {content_line}")

def test_json_conversion():
    """测试JSON数据转换"""
    print("\n📄 测试JSON数据转换")
    print("=" * 40)
    
    json_data = {
        "raw_str": "dq t = M \u22121 p t dt, dp t = \u2212\u2207 q V (q t ) dt \u2212 \u03b3M \u22121 p t dt + 2\u03b3\u03b2 \u22121 dW t .",
        "eq_num": "(4)"
    }
    
    print(f"📥 输入JSON:")
    print(f"   raw_str: {json_data['raw_str']}")
    print(f"   eq_num: {json_data['eq_num']}")
    
    result = convert_json_equation(json_data)
    equation_line = result.split('\n')[1]
    print(f"\n📤 输出LaTeX:")
    print(f"   {equation_line}")

def test_batch_conversion():
    """测试批量转换"""
    print("\n📦 测试批量转换")
    print("=" * 40)
    
    equations = [
        {"raw_str": "\u03b1 + \u03b2 = \u03b3", "eq_num": "(1)"},
        {"raw_str": "x\u00b2 + y\u00b2 = z\u00b2", "eq_num": "(2)"},
        "E = mc\u00b2",  # 字符串格式
        {"raw_str": "\u222b f(x) dx", "eq_num": "(3)"}
    ]
    
    print("📥 输入方程式列表:")
    for i, eq in enumerate(equations, 1):
        if isinstance(eq, dict):
            print(f"   {i}. {eq['raw_str']} [{eq.get('eq_num', 'no number')}]")
        else:
            print(f"   {i}. {eq}")
    
    results = batch_convert_equations(equations)
    
    print(f"\n📤 转换结果:")
    for i, result in enumerate(results, 1):
        if '\n' in result:
            equation_line = result.split('\n')[1]
        else:
            equation_line = result
        print(f"   {i}. {equation_line}")

def test_unicode_coverage():
    """测试Unicode字符覆盖"""
    print("\n🔤 测试Unicode字符覆盖")
    print("=" * 40)
    
    unicode_tests = [
        ("希腊字母", "\u03b1\u03b2\u03b3\u03b4\u03b5"),
        ("数学符号", "\u2202\u2207\u2212\u00b1\u221e"),
        ("集合符号", "\u2208\u2209\u2282\u2283\u2205"),
        ("箭头符号", "\u2192\u2190\u21d2\u21d0"),
        ("上标数字", "x\u00b2 + y\u00b3"),
        ("积分符号", "\u222b\u222c\u222d"),
    ]
    
    for category, test_str in unicode_tests:
        result = convert_equation_to_latex(test_str, equation_type="inline")
        print(f"   {category}: {test_str} → {result}")

def create_test_document():
    """创建完整的测试文档"""
    print("\n📄 创建完整测试文档")
    print("=" * 40)
    
    # 测试方程式
    equation = "dq t = M \u22121 p t dt, dp t = \u2212\u2207 q V (q t ) dt \u2212 \u03b3M \u22121 p t dt + 2\u03b3\u03b2 \u22121 dW t ."
    
    # 生成不同格式
    eq_standard = convert_equation_to_latex(equation, "(1)", "equation")
    eq_align = convert_equation_to_latex(equation, "(2)", "align")
    eq_cases = convert_equation_to_latex(equation, "(3)", "cases")
    
    # 创建完整文档
    greek_test = convert_equation_to_latex("\u03b1 + \u03b2 = \u03b3", equation_type="inline")
    math_test = convert_equation_to_latex("\u2202f/\u2202x + \u2207f = \u221e", equation_type="inline")
    set_test = convert_equation_to_latex("x \u2208 \u211d, y \u2209 \u2205", equation_type="inline")

    document = f"""\\documentclass[12pt,a4paper]{{article}}
\\usepackage[utf8]{{inputenc}}
\\usepackage[T1]{{fontenc}}
\\usepackage{{amsmath}}
\\usepackage{{amssymb}}
\\usepackage{{amsfonts}}

\\begin{{document}}

\\title{{通用方程式转换器测试}}
\\author{{Universal Equation Converter}}
\\date{{\\today}}
\\maketitle

\\section{{转换示例}}

\\subsection{{标准方程式格式}}
{eq_standard}

\\subsection{{对齐方程式格式}}
{eq_align}

\\subsection{{分段函数格式}}
{eq_cases}

\\section{{Unicode字符测试}}

希腊字母：{greek_test}

数学符号：{math_test}

集合符号：{set_test}

\\end{{document}}"""
    
    # 保存文档
    with open("universal_converter_test.tex", "w", encoding="utf-8") as f:
        f.write(document)
    
    print("✅ 测试文档已保存为: universal_converter_test.tex")
    print("💡 编译命令: pdflatex universal_converter_test.tex")

def main():
    """运行所有测试"""
    print("🧮 通用JSON到LaTeX方程式转换器 - 完整测试")
    print("=" * 60)
    
    # 运行所有测试
    test_basic_conversion()
    test_different_formats()
    test_json_conversion()
    test_batch_conversion()
    test_unicode_coverage()
    create_test_document()
    
    print(f"\n🎉 所有测试完成！")
    print(f"\n📋 总结:")
    print(f"   ✅ 基本转换功能正常")
    print(f"   ✅ 多种输出格式支持")
    print(f"   ✅ JSON数据转换正常")
    print(f"   ✅ 批量转换功能正常")
    print(f"   ✅ Unicode字符覆盖完整")
    print(f"   ✅ 测试文档已生成")

if __name__ == "__main__":
    main()
