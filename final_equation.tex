\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{amsfonts}

\begin{document}

\title{完美转换的方程式}
\maketitle

\section{原始JSON数据}
\begin{verbatim}
"eq_spans": [
    {
        "start": 0,
        "end": 8,
        "text": "EQUATION",
        "ref_id": "EQREF",
        "raw_str": "dq t = M \u22121 p t dt, dp t = \u2212\u2207 q V (q t ) dt \u2212 \u03b3M \u22121 p t dt + 2\u03b3\u03b2 \u22121 dW t .",
        "eq_num": "(4)"
    }
]
\end{verbatim}

\section{转换后的LaTeX方程式}

\subsection{版本1：基本转换}
\begin{equation}
dq_t = M^{-1} p_t \, dt, \quad dp_t = -\nabla_q V(q_t) \, dt - \gamma M^{-1} p_t \, dt + \sqrt{2\gamma\beta^{-1}} \, dW_t
\label{eq:4}
\end{equation}

\subsection{版本2：更好的格式}
\begin{align}
dq_t &= M^{-1} p_t \, dt \label{eq:4a} \\
dp_t &= -\nabla_q V(q_t) \, dt - \gamma M^{-1} p_t \, dt + \sqrt{2\gamma\beta^{-1}} \, dW_t \label{eq:4b}
\end{align}

\subsection{版本3：紧凑格式}
\begin{equation}
\begin{cases}
dq_t = M^{-1} p_t \, dt \\
dp_t = -\nabla_q V(q_t) \, dt - \gamma M^{-1} p_t \, dt + \sqrt{2\gamma\beta^{-1}} \, dW_t
\end{cases}
\label{eq:4}
\end{equation}

\section{转换说明}

\begin{itemize}
\item \texttt{\textbackslash u22121} $\rightarrow$ \texttt{\^{}\{-1\}} (上标-1)
\item \texttt{\textbackslash u2212} $\rightarrow$ \texttt{-} (减号)
\item \texttt{\textbackslash u2207} $\rightarrow$ \texttt{\textbackslash nabla} (nabla算子)
\item \texttt{\textbackslash u03b3} $\rightarrow$ \texttt{\textbackslash gamma} (希腊字母γ)
\item \texttt{\textbackslash u03b2} $\rightarrow$ \texttt{\textbackslash beta} (希腊字母β)
\item \texttt{q t} $\rightarrow$ \texttt{q\_t} (下标)
\item \texttt{V (q t )} $\rightarrow$ \texttt{V(q\_t)} (函数调用)
\item \texttt{2γβ⁻¹} $\rightarrow$ \texttt{\textbackslash sqrt\{2\textbackslash gamma\textbackslash beta\^{}\{-1\}\}} (平方根)
\end{itemize}

\end{document}
