方程式转换摘要报告
==================================================

总共找到方程式: 4

按章节分布:
  - Spectral methods for multiscale stochastic differential equations: 2 个方程式
  - Derivative-free methods for inverse problems: 1 个方程式
  - Research program title: Sampling in high dimensions: from mathematical analysis to practical algorithms: 1 个方程式

==================================================

方程式 1:
  章节: Spectral methods for multiscale stochastic differential equations
  原始: dX t = F (X t ) dt + A(X t ) dW (t),
  编号: (1)
  LaTeX: dX t = F(X_{t} ) \, \quad dt + A(X_{t} ) \, \quad dW(t),

------------------------------

方程式 2:
  章节: Spectral methods for multiscale stochastic differential equations
  原始: −L 0 (x) ϕ(x, y) = f (x, y).
  编号: (2)
  LaTeX: - L_{0} (x) \phi(x, \quad y) = f(x, \quad y)

------------------------------

方程式 3:
  章节: Derivative-free methods for inverse problems
  原始: y = G(u) + η,
  编号: (3)
  LaTeX: y = G(u) + \eta,

------------------------------

方程式 4:
  章节: Research program title: Sampling in high dimensions: from mathematical analysis to practical algorithms
  原始: dq t = M −1 p t dt, dp t = −∇ q V (q t ) dt − γM −1 p t dt + 2γβ −1 dW t .
  编号: (4)
  LaTeX: dq t = M - 1 p_{t} \, \quad dt, \quad dp t = - \nabla q_{V} (q_{t} ) \, \quad dt - \gamma M - 1 p_{t} \, \quad dt + 2\gamma\beta - 1 \, \quad dW t

------------------------------

