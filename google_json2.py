import json
import sys
import re
import os

def escape_latex(text):
    """
    Escapes special LaTeX characters in a string, handling basic cases.
    Assumes document uses UTF-8, so accented characters in input JSON
    (like \u00c9) should pass through directly to the output .tex file.
    IMPORTANT: This function is for TEXT mode. It does NOT preserve
               LaTeX math syntax ($...$, \[...\], etc.). Math content
               extracted from specific fields (like eq_spans.raw_str)
               should NOT be passed to this function.
    """
    if not isinstance(text, str):
        return str(text) # Handle non-string input gracefully

    latex_chars = {
        '&': '\\&', '%': '\\%', '$': '\\$', '#': '\\#', '_': '\\_',
        '{': '\\{', '}': '\\}', '~': '\\textasciitilde{}',
        '^': '\\textasciicircum{}', '\\': '\\textbackslash{}',
        '<': '\\textless{}', '>': '\\textgreater{}',
        '|': '\\textbar{}',
        # Quotes: convert straight quotes to LaTeX smart quotes or neutral quotes
        # Note: Using '' for double quotes often triggers smart quotes by default in LaTeX
        "'": "'",   # Standard apostrophe
        '"': "''",  # Standard double quote
        # Hyphens: Convert multiple hyphens to emdash or endash
        '---': '---', # Emdash
        '--': '--',   # Endash
        # Tilde in text mode
        '~': '\\textasciitilde{}',
        # Caret in text mode
        '^': '\\textasciicircum{}',
        # Backslash in text mode
        '\\': '\\textbackslash{}',
        # Less/greater than in text mode
        '<': '\\textless{}',
        '>': '\\textgreater{}',
    }
    # Build a regex pattern for all characters to escape, sorted by length descending
    # to handle multi-character sequences like '---' before '-'
    # Also escape regex special characters within the keys themselves
    pattern = '|'.join(re.escape(key) for key in sorted(latex_chars.keys(), key=len, reverse=True))

    # Use a replacement function to look up the correct LaTeX escape
    return re.sub(pattern, lambda match: latex_chars[match.group(0)], text)


def format_author(author_data):
    """Formats a single author dictionary into a string for LaTeX authblk."""
    # This JSON structure uses 'first', 'middle', 'last'
    first_name = author_data.get('first', '').strip()
    middle_names = " ".join(author_data.get('middle', [])).strip()
    last_name = author_data.get('last', '').strip()
    suffix = author_data.get('suffix', '').strip()

    name_parts = []
    if first_name:
        name_parts.append(first_name)
    if middle_names:
        name_parts.append(middle_names)
    if last_name:
        name_parts.append(last_name)
    if suffix:
         name_parts.append(suffix) # Suffix like Jr., III etc.

    full_name = " ".join(name_parts).strip()

    # Escape LaTeX special characters in the name (only for text parts)
    return escape_latex(full_name)

def format_reference(ref_id, ref_data):
    """
    Formats a single reference dictionary into a LaTeX \bibitem string.
    Uses the provided ref_id as the label key.
    """
    # Use the raw_text if available, otherwise try to build from parsed fields.
    # The 'bib_entries' structure is a bit different from standard GROBID TEI biblio.
    # Let's prioritize raw_text as it's often the most complete.
    raw_text = ref_data.get('raw_text', '').strip()

    if raw_text:
         # Just use the raw text after the bibitem key
         content = escape_latex(raw_text)
    else:
        # Fallback: Try to build from parsed fields in the JSON structure
        # This is a simplified attempt and might not cover all cases
        parts = []
        authors_list = ref_data.get('authors', []) # List of author dicts
        if authors_list:
            author_names = []
            for author in authors_list:
                 # These author dicts seem simpler than header authors
                 auth_parts = []
                 if author.get('first'): auth_parts.append(author['first'])
                 if author.get('middle'): auth_parts.extend(author['middle'])
                 if author.get('last'): auth_parts.append(author['last'])
                 name = " ".join(auth_parts).strip()
                 if name: author_names.append(escape_latex(name)) # Escape author names in bib list

            if author_names:
                parts.append(", ".join(author_names))


        title = ref_data.get('title', '').strip()
        if title:
            # Basic heuristic for title style (might need refinement)
            # Assume articles/chapters need quotes, books/journals italics.
            # This JSON doesn't clearly tag type, so guess based on venue presence.
            if ref_data.get('venue') or ref_data.get('publisher') or ref_data.get('book_title'):
                 parts.append(f"``{escape_latex(title)}''") # Assume article/chapter
            else:
                 parts.append(f"\\textit{{{escape_latex(title)}}}") # Assume book/report/thesis etc.

        # Journal/Book/Conference info
        venue = ref_data.get('venue', bib_data.get('book_title', '')).strip() # Prioritize venue/journal, then book title
        if venue:
            parts.append(f"\\textit{{{escape_latex(venue)}}}")

        volume = ref_data.get('volume', '').strip()
        issue = ref_data.get('issue', '').strip()
        if volume or issue:
            vol_issue = []
            if volume: vol_issue.append(escape_latex(volume))
            if issue: vol_issue.append(f"({escape_latex(issue)})")
            if vol_issue: parts.append(" ".join(vol_issue))

        pages = ref_data.get('pages', '').strip()
        if pages:
            parts.append(f"pp. {escape_latex(pages)}")

        year = ref_data.get('year', '').strip()
        if year:
             parts.append(f"({escape_latex(year)})")

        # Combine parsed parts
        content = ", ".join(parts) + "." if parts else "Unknown reference details."

    # Ensure ref_id is valid for LaTeX label
    # Remove characters not allowed in LaTeX labels, replace with underscore
    valid_ref_key = re.sub(r'[^a-zA-Z0-9:_.-]', '_', ref_id)
    if not valid_ref_key:
         valid_ref_key = f"ref_{abs(hash(ref_id))}" # Fallback if ref_id becomes empty

    return f"\\bibitem{{{escape_latex(valid_ref_key)}}}\n{content}\n"


def process_content_blocks(blocks, ref_entries, bib_entries):
    """Processes a list of content blocks (e.g., body_text, back_matter)."""
    block_latex = ""
    if not blocks:
        return block_latex

    current_section_title = None
    current_sec_num = None

    for i, block in enumerate(blocks):
        section_title = block.get('section', '').strip()
        sec_num = block.get('sec_num', '') # Keep as is to check for change

        # Check if a new section heading is needed
        is_new_section = False
        # Check if section title exists and is different, OR if sec_num changes
        # An empty section title might mean it's part of the previous section,
        # UNLESS the previous block also had an empty section title.
        # A simplified approach: treat non-empty section titles as new sections
        # only if the title or number has changed from the previous *rendered* section.
        # Also handle the very first block if it has a section title/num.
        if section_title or sec_num: # Block has *some* section info
            if section_title != current_section_title or sec_num != current_sec_num:
                 is_new_section = True
                 current_section_title = section_title
                 current_sec_num = sec_num
        # If section_title and sec_num are both empty, it's likely a continuation
        # of the previous section/paragraph block. No new section command needed.

        if is_new_section:
             # Determine section command based on sec_num structure
             # 1 -> section, 1.1 -> subsection, 1.1.1 -> subsubsection etc.
             # Or use section* if no sec_num.
             escaped_section_title = escape_latex(section_title if section_title else 'Untitled Section') # Use placeholder if title is empty but num exists

             if sec_num:
                 level = sec_num.count('.') + 1
                 if level == 1:
                     block_latex += f"\\section{{{escaped_section_title}}}\n"
                 elif level == 2:
                      block_latex += f"\\subsection{{{escaped_section_title}}}\n"
                 elif level == 3:
                      block_latex += f"\\subsubsection{{{escaped_section_title}}}\n"
                 elif level == 4:
                      block_latex += f"\\paragraph{{{escaped_section_title}.}}\n" # paragraph is usually followed by text on the same line
                 else:
                      block_latex += f"\\subsection*{{{escaped_section_title}}}\n" # Fallback for deeper or unusual numbering
             elif section_title: # Section title but no number -> unnumbered section
                 block_latex += f"\\section*{{{escaped_section_title}}}\n"
             # If sec_num and section_title are both empty, is_new_section is False, handled above.
             block_latex += "\n" # Add a blank line after section command

        # Process the text content within the block
        text_content = block.get('text', '')
        cite_spans = block.get('cite_spans', [])
        ref_spans = block.get('ref_spans', []) # These might link to ref_entries or bib_entries
        eq_spans = block.get('eq_spans', [])

        processed_text = ""
        last_offset = 0

        # Collect all inline elements and sort by start offset
        inline_elements = []
        for span in cite_spans:
             span['type'] = 'cite'
             inline_elements.append(span)
        for span in ref_spans:
             span['type'] = 'ref' # May refer to figures/tables (ref_entries) or bib (bib_entries)
             inline_elements.append(span)
        # --- IMPORTANT FIX: eq_spans are treated as blocks, not inline spans within text ---
        # GROBID's eq_spans usually cover a placeholder like "EQUATION".
        # We should extract the raw_str equation and insert it as a DISPLAY math block,
        # interrupting the paragraph flow.
        # We will process eq_spans SEPARATELY before processing the text with inline spans.

        # Sort inline elements (cite/ref spans) by start offset for text processing
        inline_text_spans = sorted([e for e in inline_elements if e['type'] in ['cite', 'ref']],
                                    key=lambda x: x.get('start', 0))


        # First, process the text content and inline cite/ref spans
        for element in inline_text_spans:
            start = element.get('start', 0)
            end = element.get('end', 0) # End of the text span corresponding to the element

            # Add text before this inline element, properly escaped
            processed_text += escape_latex(text_content[last_offset:start])

            # Process the inline element based on its type
            element_type = element['type']
            element_ref_id = element.get('ref_id') # Used for cite/ref


            if element_type == 'cite' and element_ref_id:
                 # Check if ref_id exists in bib_entries before creating cite
                 valid_ref_key = re.sub(r'[^a-zA-Z0-9:_.-]', '_', element_ref_id) # Match format_reference's label
                 if valid_ref_key in bib_entries:
                     processed_text += f"\\cite{{{escape_latex(valid_ref_key)}}}"
                 else:
                     # Fallback if ref_id not found in bib_entries
                     # Output the original text span as fallback
                     print(f"Warning: cite ref_id '{element_ref_id}' (processed to '{valid_ref_key}') not found in bib_entries. Outputting original text span.")
                     processed_text += escape_latex(text_content[start:end])


            elif element_type == 'ref' and element_ref_id:
                 # In this JSON, ref_spans often point to BIBREF.
                 # If they point to ref_entries keys (FIGREF, TABREF etc.), use \ref.
                 # If they point to BIBREF, they are likely meant as citations.
                 # Let's check if the key exists in ref_entries or bib_entries.
                 valid_ref_key = re.sub(r'[^a-zA-Z0-9:_.-]', '_', element_ref_id) # Match format_reference's label and potential ref_entries keys
                 if valid_ref_key in ref_entries:
                     # Assuming ref_id maps to a figure/table label
                     processed_text += f"\\ref{{{escape_latex(valid_ref_key)}}}"
                 elif valid_ref_key in bib_entries:
                      # Treat ref_span pointing to BIBREF as a citation
                      processed_text += f"\\cite{{{escape_latex(valid_ref_key)}}}"
                 else:
                     # Fallback if ref_id not found in either
                     # Output the original text span as fallback
                     print(f"Warning: ref_span ref_id '{element_ref_id}' (processed to '{valid_ref_key}') not found in ref_entries or bib_entries. Outputting original text span.")
                     processed_text += escape_latex(text_content[start:end])


            # Important: Update the last_offset to the end of the *original text span*
            # covered by the inline element, so we don't re-process it.
            last_offset = end

        # Add any remaining text after the last inline element
        processed_text += escape_latex(text_content[last_offset:])

        # Add the processed text as a paragraph block
        # Only add if there's actual text content generated
        if processed_text.strip():
             block_latex += processed_text.strip() + "\n\n" # Use two newlines for a paragraph break


        # --- Now, process display equations separately ---
        # These are typically placed *after* the paragraph they interrupt/follow.
        for element in sorted(eq_spans, key=lambda x: x.get('start', 0)): # Sort equations by position
             element_raw_str = element.get('raw_str', '').strip()
             element_eq_num = element.get('eq_num', '').strip()

             if element_raw_str:
                 eq_label = ""
                 if element_eq_num:
                     # Create a valid label from eq_num
                     valid_eq_label = re.sub(r'[^a-zA-Z0-9:_.-]', '_', element_eq_num)
                     if valid_eq_label:
                          eq_label = f"\\label{{eq:{valid_eq_label}}}"
                          # Use equation environment for numbered equations
                          block_latex += "\\begin{equation}\n"
                          # Do NOT escape the raw equation string
                          block_latex += element_raw_str + "\n"
                          block_latex += eq_label + "\n"
                          block_latex += "\\end{equation}\n\n"
                     else:
                          # No valid number, use unnumbered display math
                          block_latex += "\\[\n"
                          block_latex += element_raw_str + "\n"
                          block_latex += "\\]\n\n"
                          print(f"Warning: Could not create valid label for equation with number '{element_eq_num}'. Using unnumbered math.")
                 else:
                      # No number, use unnumbered display math
                      block_latex += "\\[\n"
                      # Do NOT escape the raw equation string
                      block_latex += element_raw_str + "\n"
                      block_latex += "\\]\n\n"
                      # print(f"Info: Unnumbered equation added: {element_raw_str[:50]}...") # Optional debug

        # --- Handle Figure/Table entries (ref_entries) that might be standalone blocks ---
        # The provided JSON has FIGREF/TABREF in ref_entries but they don't seem
        # directly linked to body_text blocks as figures/tables with graphics.
        # They appear more like resume/list items.
        # However, a typical GROBID JSON *might* have blocks structured like:
        # { "head": "Figure 1", "graphic": "path/to/img.png", "figDesc": "Caption" }
        # Let's add a basic check for common figure/table block structures,
        # but note this JSON sample doesn't use it this way in body_text.
        # The FIGREF/TABREF in ref_entries look more like descriptions than objects to place.
        # For this specific JSON structure, FIGREF/TABREF in ref_entries are *metadata*
        # about things potentially *referenced* in the text via ref_spans, but not
        # blocks to be rendered themselves. The previous code's figure/table handling
        # was based on *different* GROBID JSON structures. Let's remove that logic
        # as it doesn't match *this* JSON and the ref_entries here are not figure/table *objects*
        # to be placed. They are descriptions indexed by a key.
        pass # Removed placeholder figure/table handling logic here


    return block_latex


def grobid_json_to_latex(json_data):
    """Converts a GROBID-like JSON dictionary into a LaTeX string."""
    latex_string = ""

    # --- Preamble ---
    latex_string += "\\documentclass{article}\n"
    latex_string += "\\usepackage[utf8]{inputenc}\n" # Required for special characters like accents
    latex_string += "\\usepackage[T1]{fontenc}\n"    # Recommended font encoding
    latex_string += "\\usepackage{graphicx}\n"       # For figures (even if not auto-placed, needed for refs)
    latex_string += "\\usepackage{hyperref}\n"       # For links, citations, and references
    latex_string += "\\usepackage{authblk}\n"        # For author list with affiliations
    latex_string += "\\usepackage{amsmath}\n"        # For mathematical equations (needed for \\[, \\], equation)
    # latex_string += "\\usepackage{booktabs}\n"     # For better tables (if manually added)
    latex_string += "\\usepackage{geometry}\n"
    latex_string += "\\geometry{a4paper, margin=1in}\n" # Example margins, adjust as needed
    latex_string += "\n"

    # --- Metadata ---
    title = json_data.get('title', 'Untitled Document')
    latex_string += f"\\title{{{escape_latex(title)}}}\n"

    authors = json_data.get('authors', [])
    if authors:
        # authblk handles authors like \author{Name1 \and Name2}
        # or \author{Name1}\affil{Affil1}\author{Name2}\affil{Affil2}
        # This simplified version just lists names separated by \and.
        author_names = [format_author(author) for author in authors]
        # Filter out empty names
        author_names = [name for name in author_names if name]
        if author_names:
             latex_string += "\\author{%s}\n" % " \\and ".join(author_names)
        # Detailed affiliation handling requires more complex parsing of 'affiliation'

    # You could try to parse a date from header or other fields if available
    latex_string += "\\date{}" # No date by default

    latex_string += "\n\\begin{document}\n"

    latex_string += "\\maketitle\n"

    # --- Abstract ---
    # The JSON has abstract text directly under the root and also in pdf_parse.abstract
    # Let's use the root level abstract if available, otherwise try pdf_parse.abstract
    abstract_text = json_data.get('abstract', '').strip()
    if not abstract_text:
         # Check pdf_parse.abstract, which is a list of blocks
         abstract_blocks = json_data.get('pdf_parse', {}).get('abstract', [])
         # Concatenate text from abstract blocks
         # Escape abstract text, but handle potential inline math if GROBID tagged it.
         # For simplicity with this JSON structure, just concatenate and escape.
         abstract_text = "\n\n".join(block.get('text', '').strip() for block in abstract_blocks if block.get('text'))


    if abstract_text:
        latex_string += "\\begin{abstract}\n"
        # Escape abstract text as it's generally plain text
        latex_string += escape_latex(abstract_text) + "\n"
        latex_string += "\\end{abstract}\n\n"

    # --- Body ---
    # Process body_text blocks, using the content processor
    body_blocks = json_data.get('pdf_parse', {}).get('body_text', [])
    ref_entries = json_data.get('pdf_parse', {}).get('ref_entries', {}) # Pass ref_entries for \ref lookup
    bib_entries = json_data.get('pdf_parse', {}).get('bib_entries', {}) # Pass bib_entries for \cite lookup


    latex_string += process_content_blocks(body_blocks, ref_entries, bib_entries)


    # --- Back Matter (Optional) ---
    back_matter_blocks = json_data.get('pdf_parse', {}).get('back_matter', [])
    if back_matter_blocks:
        # Add a break or header before back matter if it's distinct
        # The provided JSON's back_matter contains presentation list under 'funding' section?
        # Let's process it with the same logic, section headers included.
        latex_string += "% --- Back Matter ---\n"
        latex_string += process_content_blocks(back_matter_blocks, ref_entries, bib_entries)


    # --- References ---
    # GROBID usually puts references in back_matter.
    # Your JSON has parsed bib_entries separately.
    # Let's generate the bibliography from bib_entries.
    if bib_entries:
        # Ensure we haven't already added a References section (e.g., in back_matter)
        # Simple check: Does the latex string already contain a section command before this?
        # A more robust way would track sections added.
        # For now, just add the section heading.
        latex_string += "\\section*{References}\n" # Use unnumbered section for bibliography

        # Use thebibliography environment with bibitem for compatibility with \cite
        # The argument specifies the widest label, e.g., {99} for up to 99 items.
        # Count the number of references to get a better estimate, but 99 is common.
        # Max length of number or BIBREF key? Let's estimate based on count for the label width argument.
        num_bib_entries = len(bib_entries)
        if num_bib_entries > 0:
             widest_label_num_digits = len(str(num_bib_entries))
             # LaTeX labels can also be strings. Using '9'*digits is common for numbered lists.
             # If using BIBREF keys, max key length might be better, but let's stick to number estimate for the *width* argument.
             widest_label_width_arg = '9' * max(widest_label_num_digits, 2) # Ensure at least two digits wide (like {99})
        else:
             widest_label_width_arg = '9' # Default if no refs

        latex_string += f"\\begin{{thebibliography}}{{{widest_label_width_arg}}}\n"
        # Sort bib_entries by ref_id key for consistent output order
        for ref_id, ref_data in sorted(bib_entries.items()):
            latex_string += format_reference(ref_id, ref_data)
        latex_string += "\\end{thebibliography}\n"


    # --- End Document ---
    latex_string += "\n\\end{document}\n"

    return latex_string

# --- Main execution block ---
if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python json_to_latex.py <input_json_file> <output.tex_file>")
        print("Example: python json_to_latex.py mcf_analytical_presentation.json output.tex")
        sys.exit(1)

    input_json_file = sys.argv[1]
    output_tex_file = sys.argv[2]

    try:
        # Read the JSON file
        # Using 'utf-8' as it's standard and handles characters like accents
        with open(input_json_file, 'r', encoding='utf-8') as f:
            grobid_data = json.load(f)

        # Convert JSON to LaTeX string
        latex_output = grobid_json_to_latex(grobid_data)

        # Write the LaTeX string to a .tex file
        with open(output_tex_file, 'w', encoding='utf-8') as f:
            f.write(latex_output)

        print(f"Successfully converted '{input_json_file}' to '{output_tex_file}'")
        print("Note: You may need to manually adjust the generated .tex file for detailed formatting, figures, tables, and complex structures.")
        print("Compile the .tex file using a LaTeX distribution (e.g., pdflatex).")
        print("Check the output for WARNINGs about unfound references or invalid equation numbers.")

    except FileNotFoundError:
        print(f"Error: Input file not found at '{input_json_file}'")
        sys.exit(1)
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from '{input_json_file}'. Make sure it's valid JSON.")
        sys.exit(1)
    except Exception as e:
        print(f"An error occurred during conversion: {e}")
        # Optional: Print traceback for debugging
        # import traceback
        # traceback.print_exc()
        sys.exit(1)