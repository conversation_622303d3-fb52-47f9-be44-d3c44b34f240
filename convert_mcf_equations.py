#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
转换 mcf_analytical_presentation.json 文件中的所有方程式
"""

import json
from universal_equation_converter import convert_json_equation, convert_equation_to_latex

def extract_and_convert_equations(json_file_path):
    """
    从JSON文件中提取并转换所有方程式
    """
    print("🔍 正在分析JSON文件中的方程式...")
    
    # 读取JSON文件
    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    equations_found = []
    
    # 搜索所有包含eq_spans的部分
    def search_equations(obj, path=""):
        if isinstance(obj, dict):
            if 'eq_spans' in obj and obj['eq_spans']:
                for eq_span in obj['eq_spans']:
                    if eq_span.get('raw_str'):
                        equations_found.append({
                            'path': path,
                            'section': obj.get('section', 'Unknown'),
                            'eq_data': eq_span
                        })
            
            for key, value in obj.items():
                search_equations(value, f"{path}.{key}" if path else key)
        
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                search_equations(item, f"{path}[{i}]" if path else f"[{i}]")
    
    # 搜索方程式
    search_equations(data)
    
    print(f"📊 找到 {len(equations_found)} 个方程式")
    
    if not equations_found:
        print("❌ 未找到任何方程式")
        return
    
    # 转换每个方程式
    converted_equations = []
    
    for i, eq_info in enumerate(equations_found, 1):
        eq_data = eq_info['eq_data']
        section = eq_info['section']
        
        print(f"\n📐 方程式 {i}:")
        print(f"   位置: {section}")
        print(f"   原始: {eq_data.get('raw_str', '')}")
        print(f"   编号: {eq_data.get('eq_num', 'N/A')}")
        
        # 转换方程式
        try:
            # 标准格式
            latex_eq = convert_json_equation(eq_data, "equation")
            
            # 对齐格式（如果方程式包含逗号分隔的多个部分）
            if ', ' in eq_data.get('raw_str', ''):
                latex_align = convert_json_equation(eq_data, "align")
            else:
                latex_align = None
            
            converted_equations.append({
                'original': eq_data,
                'section': section,
                'latex_equation': latex_eq,
                'latex_align': latex_align
            })
            
            # 显示转换结果
            equation_content = latex_eq.split('\n')[1] if '\n' in latex_eq else latex_eq
            print(f"   转换: {equation_content}")
            
        except Exception as e:
            print(f"   ❌ 转换失败: {e}")
    
    return converted_equations

def create_latex_document(converted_equations, output_file="mcf_equations_converted.tex"):
    """
    创建包含所有转换方程式的LaTeX文档
    """
    print(f"\n📄 创建LaTeX文档: {output_file}")
    
    # 按章节分组
    sections = {}
    for eq in converted_equations:
        section = eq['section']
        if section not in sections:
            sections[section] = []
        sections[section].append(eq)
    
    # 生成LaTeX文档
    latex_content = r"""\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage[french]{babel}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{amsfonts}
\usepackage[margin=2.5cm]{geometry}

\begin{document}

\title{Équations extraites de mcf\_analytical\_presentation.json}
\author{Conversion automatique}
\date{\today}
\maketitle

\tableofcontents
\newpage

"""
    
    # 添加每个章节的方程式
    for section_name, equations in sections.items():
        # 清理章节名称
        clean_section = section_name.replace('_', '\\_').replace('&', '\\&')
        latex_content += f"\\section{{{clean_section}}}\n\n"
        
        for i, eq in enumerate(equations, 1):
            latex_content += f"\\subsection{{Équation {i}}}\n\n"
            
            # 原始信息 - 清理Unicode字符以避免LaTeX错误
            raw_str_clean = eq['original'].get('raw_str', '')
            # 替换常见的Unicode字符
            unicode_replacements = {
                '−': '-',  # Unicode minus sign
                '∇': '\\nabla',
                'γ': '\\gamma',
                'β': '\\beta',
                'η': '\\eta',
                'ϕ': '\\phi',
                '∞': '\\infty',
                '≤': '\\leq',
                '≥': '\\geq',
                '≠': '\\neq',
                '±': '\\pm',
                '∓': '\\mp',
                '×': '\\times',
                '÷': '\\div',
                '∈': '\\in',
                '∉': '\\notin',
                '⊂': '\\subset',
                '⊃': '\\supset',
                '∪': '\\cup',
                '∩': '\\cap',
                '∅': '\\emptyset'
            }

            for unicode_char, latex_equiv in unicode_replacements.items():
                raw_str_clean = raw_str_clean.replace(unicode_char, latex_equiv)

            raw_str_clean = raw_str_clean.replace('_', '\\_').replace('&', '\\&').replace('%', '\\%')
            eq_num = eq['original'].get('eq_num', 'N/A')

            latex_content += f"\\textbf{{Données originales:}}\n"
            latex_content += f"\\begin{{verbatim}}\n"
            latex_content += f"raw_str: {raw_str_clean}\n"
            latex_content += f"eq_num: {eq_num}\n"
            latex_content += f"\\end{{verbatim}}\n\n"
            
            # 转换后的方程式
            latex_content += f"\\textbf{{Format standard:}}\n"
            latex_content += eq['latex_equation'] + "\n\n"
            
            # 如果有对齐格式
            if eq['latex_align']:
                latex_content += f"\\textbf{{Format aligné:}}\n"
                latex_content += eq['latex_align'] + "\n\n"
            
            latex_content += "\\hrule\n\\vspace{1em}\n\n"
    
    latex_content += "\\end{document}"
    
    # 保存文档
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(latex_content)
    
    print(f"✅ LaTeX文档已保存: {output_file}")
    return output_file

def create_summary_report(converted_equations, output_file="equations_summary.txt"):
    """
    创建方程式转换摘要报告
    """
    print(f"\n📋 创建摘要报告: {output_file}")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("方程式转换摘要报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"总共找到方程式: {len(converted_equations)}\n\n")
        
        # 按章节统计
        sections = {}
        for eq in converted_equations:
            section = eq['section']
            sections[section] = sections.get(section, 0) + 1
        
        f.write("按章节分布:\n")
        for section, count in sections.items():
            f.write(f"  - {section}: {count} 个方程式\n")
        
        f.write("\n" + "=" * 50 + "\n\n")
        
        # 详细列表
        for i, eq in enumerate(converted_equations, 1):
            f.write(f"方程式 {i}:\n")
            f.write(f"  章节: {eq['section']}\n")
            f.write(f"  原始: {eq['original'].get('raw_str', '')}\n")
            f.write(f"  编号: {eq['original'].get('eq_num', 'N/A')}\n")
            
            # 提取转换后的方程式内容
            latex_content = eq['latex_equation'].split('\n')[1] if '\n' in eq['latex_equation'] else eq['latex_equation']
            f.write(f"  LaTeX: {latex_content}\n")
            f.write("\n" + "-" * 30 + "\n\n")
    
    print(f"✅ 摘要报告已保存: {output_file}")

def main():
    """主函数"""
    print("🧮 MCF分析演示文档方程式转换器")
    print("=" * 50)
    
    json_file = "output_dir/mcf_analytical_presentation.json"
    
    # 提取和转换方程式
    converted_equations = extract_and_convert_equations(json_file)
    
    if not converted_equations:
        print("❌ 没有找到可转换的方程式")
        return
    
    # 创建LaTeX文档
    latex_file = create_latex_document(converted_equations)
    
    # 创建摘要报告
    create_summary_report(converted_equations)
    
    print(f"\n🎉 转换完成！")
    print(f"📁 生成的文件:")
    print(f"   - {latex_file} (完整LaTeX文档)")
    print(f"   - equations_summary.txt (摘要报告)")
    print(f"\n💡 编译建议:")
    print(f"   pdflatex {latex_file}")

if __name__ == "__main__":
    main()
