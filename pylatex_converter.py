#!/usr/bin/env python3
"""
JSON to LaTeX converter using pylatexenc for reliable Unicode handling.
"""

import json
import re
import argparse
from typing import Dict, List, Any, Optional

try:
    from pylatexenc.latexencode import unicode_to_latex
except ImportError:
    print("Please install pylatexenc: pip install pylatexenc")
    exit(1)

class PyLaTeXConverter:
    def __init__(self):
        pass
    
    def process_unicode_text(self, text: str) -> str:
        """Process text with Unicode escape sequences and convert to LaTeX."""
        if not text:
            return ""
        
        # First handle Unicode escape sequences
        try:
            text = text.encode().decode('unicode_escape')
        except:
            pass
        
        # Use pylatexenc to convert Unicode to LaTeX
        try:
            text = unicode_to_latex(text)
        except:
            # Fallback for problematic characters
            pass
        
        return text
    
    def fix_math_subscripts(self, text: str) -> str:
        """Fix mathematical subscripts and superscripts."""
        # Fix pylatexenc output issues
        text = text.replace(r'\ensuremath{-}1', '^{-1}')
        text = text.replace(r'\ensuremath{-}', '-')
        text = text.replace(r'\ensuremath{\nabla}', r'\nabla')
        text = text.replace(r'\ensuremath{\gamma}', r'\gamma')
        text = text.replace(r'\ensuremath{\beta}', r'\beta')
        text = text.replace(r'\ensuremath{\alpha}', r'\alpha')
        text = text.replace(r'\ensuremath{\sigma}', r'\sigma')

        # Handle common patterns like "q t" -> "q_t"
        text = re.sub(r'([a-zA-Z])\s+([a-zA-Z0-9])\b', r'\1_{\2}', text)

        # Handle "X epsilon t" -> "X_{\epsilon t}"
        text = re.sub(r'([a-zA-Z])\s+\\epsilon\s+([a-zA-Z0-9])', r'\1_{\\epsilon \2}', text)

        # Handle differential notation: "dq t" -> "dq_t"
        text = re.sub(r'd([A-Z])\s+([a-zA-Z0-9])', r'd\1_{\2}', text)

        # Fix superscripts like "M^-1" -> "M^{-1}"
        text = re.sub(r'\^(-?\d+)', r'^{\1}', text)
        text = re.sub(r'\^(-?[a-zA-Z]+)', r'^{\1}', text)

        # Add proper spacing for differentials
        text = re.sub(r'\bdt\b', r'\\, dt', text)
        text = re.sub(r'\bdW\b', r'\\, dW', text)

        # Fix nabla subscripts: "\nabla_{q}" -> "\nabla_q"
        text = re.sub(r'\\nabla_\{([a-zA-Z])\}', r'\\nabla_\1', text)

        # Add space between Greek letters and variables
        text = re.sub(r'(\\gamma|\\beta|\\alpha|\\sigma)([A-Z])', r'\1 \2', text)

        # Fix specific issues from pylatexenc
        text = re.sub(r'q_\{V\}', 'V', text)  # Fix "q V" -> "q_{V}" issue

        return text
    
    def clean_text(self, text: str) -> str:
        """Clean and process text for LaTeX."""
        if not text:
            return ""
        
        # Process Unicode
        text = self.process_unicode_text(text)
        
        # Fix mathematical notation
        text = self.fix_math_subscripts(text)
        
        # Clean whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def format_equation(self, eq_text: str, eq_num: str = "") -> str:
        """Format equation with proper LaTeX structure."""
        clean_text = self.clean_text(eq_text)
        
        if eq_num:
            return f"""\\begin{{equation}}
{clean_text}
\\label{{eq:{eq_num}}}
\\end{{equation}}"""
        else:
            return f"""\\begin{{equation}}
{clean_text}
\\end{{equation}}"""
    
    def convert_json_to_latex(self, json_file: str, output_file: str):
        """Convert GROBID JSON to LaTeX."""
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extract basic info
        title = data.get('title', 'Untitled Document')
        abstract = data.get('abstract', '')
        
        # Start LaTeX document
        latex_content = [
            r'\documentclass[10pt]{article}',
            r'\usepackage[utf8]{inputenc}',
            r'\usepackage{amsmath,amssymb,mathtools}',
            r'\usepackage{geometry}',
            r'\usepackage{hyperref}',
            r'\usepackage{graphicx}',
            r'',
            r'\geometry{margin=1in}',
            r'',
            r'\begin{document}',
            r'',
            f'\\title{{{self.clean_text(title)}}}',
            r'\maketitle',
            r'',
        ]
        
        # Add abstract if present
        if abstract:
            latex_content.extend([
                r'\begin{abstract}',
                self.clean_text(abstract),
                r'\end{abstract}',
                r'',
            ])
        
        # Process body text
        body_text = data.get('pdf_parse', {}).get('body_text', [])
        current_section = None
        
        for item in body_text:
            text = item.get('text', '')
            section = item.get('section', '')
            eq_spans = item.get('eq_spans', [])
            
            # Handle section changes
            if section and section != current_section:
                current_section = section
                latex_content.append(f'\\section{{{self.clean_text(section)}}}')
                latex_content.append('')
            
            # Handle equations
            if eq_spans:
                for eq_span in eq_spans:
                    eq_text = eq_span.get('raw_str', text)
                    eq_num = eq_span.get('eq_num', '')
                    latex_content.append(self.format_equation(eq_text, eq_num))
                    latex_content.append('')
            elif text.strip() and text.strip() != "EQUATION":
                # Regular text
                processed_text = self.clean_text(text)
                if processed_text:
                    latex_content.append(processed_text)
                    latex_content.append('')
        
        # End document
        latex_content.append(r'\end{document}')
        
        # Write to file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(latex_content))
        
        print(f"Converted {json_file} to {output_file}")

def test_conversion():
    """Test the conversion with the problematic equation."""
    converter = PyLaTeXConverter()
    
    test_eq = r"dq t = M \u22121 p t dt, dp t = \u2212\u2207 q V (q t ) dt \u2212 \u03b3M \u22121 p t dt + 2\u03b3\u03b2 \u22121 dW t ."
    
    print("Testing equation conversion:")
    print(f"Input:  {test_eq}")
    
    result = converter.format_equation(test_eq, "4")
    print(f"Output:\n{result}")

def main():
    parser = argparse.ArgumentParser(description='Convert GROBID JSON to LaTeX using pylatexenc')
    parser.add_argument('input_file', nargs='?', help='Input JSON file')
    parser.add_argument('-o', '--output', help='Output LaTeX file')
    parser.add_argument('--test', action='store_true', help='Run test conversion')

    args = parser.parse_args()

    if args.test:
        test_conversion()
        return

    if not args.input_file or not args.output:
        parser.error("input_file and -o/--output are required when not using --test")

    converter = PyLaTeXConverter()
    converter.convert_json_to_latex(args.input_file, args.output)

if __name__ == "__main__":
    main()
