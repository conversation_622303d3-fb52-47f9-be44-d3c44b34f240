{"paper_id": "temp_dir\\A_Linear_Three-Phase_Load_Flow_for_Power_Distribution_Systems (1)", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-06-20T13:43:57.620461Z"}, "title": "A Linear Three-Phase Load Flow for Power Distribution Systems", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "This letter proposes a linear load flow for three-phase power distribution systems. Balanced and unbalanced operation are considered as well as the ZIP models of the loads. The methodology does not require any assumption related to the ratio. Despite its simplicity, it is very accurate compared to the conventional back-forward sweep algorithm.", "pdf_parse": {"paper_id": "temp_dir\\A_Linear_Three-Phase_Load_Flow_for_Power_Distribution_Systems (1)", "_pdf_hash": "", "abstract": [{"text": "This letter proposes a linear load flow for three-phase power distribution systems. Balanced and unbalanced operation are considered as well as the ZIP models of the loads. The methodology does not require any assumption related to the ratio. Despite its simplicity, it is very accurate compared to the conventional back-forward sweep algorithm.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "D C-power-flow is one of the most studied methodologies for analysis and operation of electric power systems [1] . However, this linear approximation is not suitable for power distribution systems due to their high ratio and unbalanced operation [2] . This letter addresses this issue by using a linear approximation on the complex plane. A radial topology is not required. PV nodes are not considered but distribution generators can be included in the cases in which the grid code compel unity power factor operation for these generators.", "cite_spans": [{"start": 109, "end": 112, "text": "[1]", "ref_id": "BIBREF0"}, {"start": 246, "end": 249, "text": "[2]", "ref_id": "BIBREF1"}], "ref_spans": [], "eq_spans": [], "section": "I. INTRODUCTION", "sec_num": null}, {"text": "Nodal voltages and currents are related by the admittance matrix as follows: (1) where represents the slack node and is the set of remaining nodes. Each nodal current is related to the voltage by the following ZIP model: (2) where (per unit representation implies ). Notice the ZIP model is linear in except for the constant power loads . This term is approximated in order to obtain a linear power flow. ", "cite_spans": [{"start": 77, "end": 80, "text": "(1)", "ref_id": "BIBREF0"}, {"start": 221, "end": 224, "text": "(2)", "ref_id": "BIBREF1"}], "ref_spans": [], "eq_spans": [], "section": "A. Basic Formulation", "sec_num": null}, {"text": "A linear approximation is developed on the complex numbers [3] and not on the reals as in the conventional load flow formulations. The function is analytic for all . Its Taylor series around zero is (3) by neglecting high order terms and defining a linear form is obtained (4) The error in percentage for this approximation is calculated by defining a function . This function is evaluated in each point inside the filled area in Fig. 1 (a) resulting in the filled area in Fig. 1(b) . For example, the error for (i.e., ) is around and decreases as approaches 1. This property is used for the power flow formulation.", "cite_spans": [{"start": 59, "end": 62, "text": "[3]", "ref_id": "BIBREF2"}, {"start": 199, "end": 202, "text": "(3)", "ref_id": "BIBREF2"}, {"start": 273, "end": 276, "text": "(4)", "ref_id": "BIBREF3"}], "ref_spans": [{"start": 435, "end": 436, "text": "1", "ref_id": "FIGREF0"}, {"start": 478, "end": 482, "text": "1(b)", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "B. Linear Approximation", "sec_num": null}, {"text": "A linear expression for the nodal current ( 2) is obtained as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. Approximated Load Flow", "sec_num": null}, {"text": "(5) by using (1) and after rearrange some terms, a linear formulation is obtained (6) with (7) (8) (9) 0885-8950 © 2015 IEEE. Personal use is permitted, but republication/redistribution requires IEEE permission. See http://www.ieee.org/publications_standards/publications/rights/index.html for more information. Authorized licensed use limited to: Universita degli Studi di Genova. Downloaded on September 13,2024 at 08:05:25 UTC from IEEE Xplore. Restrictions apply.", "cite_spans": [{"start": 13, "end": 16, "text": "(1)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "C. Approximated Load Flow", "sec_num": null}, {"text": "Notice that (6) requires to be solved in rectangular representation as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. Approximated Load Flow", "sec_num": null}, {"text": "(10)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. Approximated Load Flow", "sec_num": null}, {"text": "where and indicate real and imaginary part, respectively.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. Approximated Load Flow", "sec_num": null}, {"text": "The proposed methodology is extendable to three-phase unbalanced power distribution systems. A three-phase admittance matrix is required.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D. Extension to the Unbalanced Case", "sec_num": null}, {"text": "are phase-neutral voltages. Subindex in (1) represents in this case three nodes corresponding to each phase. The size of the problem is increased but (6) remains as an accurate approximation. Notice that angles on the three-phase system are not necessarily close to 0, where (3) is valid. Therefore, it is required to define a rotation constant for each node, where according to the phase.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D. Extension to the Unbalanced Case", "sec_num": null}, {"text": "On the other hand, for delta connected loads, it is required to define a matrix that converts phase voltage into line voltages [i.e., ]. After including these new terms, the constant matrices in (6) are given by (11)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D. Extension to the Unbalanced Case", "sec_num": null}, {"text": "(12) (13)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D. Extension to the Unbalanced Case", "sec_num": null}, {"text": "where is the conventional product and is the Hadamard product (i.e., the Matlab element-wise multiplication).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D. Extension to the Unbalanced Case", "sec_num": null}, {"text": "In order to illustrate the application of the proposed method, consider the IEEE 37-node test feeder [4] . Loading in this system is very unbalanced. Furthermore, they are delta connected. Results are evaluated in terms of the voltage error.", "cite_spans": [{"start": 101, "end": 104, "text": "[4]", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "III. RESULTS", "sec_num": null}, {"text": "Let us define where is the voltage in the node , calculated by using the conventional back-forward sweep algorithm, and is the voltage calculated by the proposed methodology (do not confuse with defined in the previous section). Results for the three-phase and the singlephase systems are depicted in Fig. 2 . Maximum error for the balanced case is . This is a low error for a noniterative load flow. For example, the maximum error for the first iteration of the back-forward sweep algorithm is (i.e., five times higher).", "cite_spans": [], "ref_spans": [{"start": 306, "end": 307, "text": "2", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "III. RESULTS", "sec_num": null}, {"text": "Maximum error for the three-phase system is . This error is satisfactory for many practical applications. The error is not affected by the type of loads, unbalance or ratio in distribution lines. However, it is affected by the number of constant power loads and the minimum voltage of the system. Minimum voltage in the IEEE 37-node test feeder is 0.9846 pu. This explains the low error. Simulations were performed with different load levels. Two main cases were considered: constant power loads (P) and ZIP model. Results are given in Table I . As expected, exactitude decreases as the loads increase. Neverthe- TABLE I SIMULATION RESULTS WITH DIFFERENT LOAD MODELS less, the proposed linear load flow gives relatively low errors even in highly loaded systems.", "cite_spans": [], "ref_spans": [{"start": 542, "end": 543, "text": "I", "ref_id": null}], "eq_spans": [], "section": "III. RESULTS", "sec_num": null}, {"text": "A linear power flow for power distribution systems based on a rectangular formulation was proposed. The methodology demonstrated to be valid for balanced and unbalanced power systems. Results were very accurate regardless of the ratio. Exactitude of methodology can be estimated by the minimum voltage on the system. Very low voltages increase the error. Potential applications of the proposed methodology include convex optimization, optimal power flow, and distribution system dynamics among others. PV nodes as well as other controls usually present in power distribution systems are not considered. Further work will consider these aspects as well as theoretical and practical consequences of the approximation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "IV. CONCLUSIONS", "sec_num": null}, {"text": "Manuscript received July 02, 2014; revised September 10, 2014; accepted December 12, 2014. Date of publication January 29, 2015; date of current version December 18, 2015. Paper no. PESL-00098-2014. The author is with the Department of Electrical Engineering, Universidad Tecnológica de Pereira, Pereira, Colombia (e-mail: <EMAIL>. co). Color versions of one or more of the figures in this paper are available online at http://ieeexplore.ieee.org. Digital Object Identifier 10.1109/TPWRS.2015.2394296", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "DC power flow revisited", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Jardim", "suffix": ""}, {"first": "O", "middle": [], "last": "Alsac", "suffix": ""}], "year": null, "venue": "IEEE Trans. Power Syst", "volume": "24", "issue": "3", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, \"DC power flow revisited,\" IEEE Trans. Power Syst., vol. 24, no. 3, pp. 1290, 1300, Aug. 2009.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "New method for the analysis of distribution networks", "authors": [{"first": "R", "middle": ["G"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1990, "venue": "IEEE Trans. Power Del", "volume": "5", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> <PERSON><PERSON>, \"New method for the analysis of distribution net- works,\" IEEE Trans. Power Del., vol. 5, no. 1, pp. 391, 396, Jan. 1990.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Complex Variables", "authors": [{"first": "F", "middle": [], "last": "Flanigan", "suffix": ""}], "year": 2010, "venue": "Dover Books on Mathematics", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, Complex Variables, Dover Books on Mathematics , Ed. New York, NY, USA: Dover, 2010.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Radial distribution test feeders", "authors": [{"first": "W", "middle": ["H"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1991, "venue": "IEEE Trans. Power Syst", "volume": "6", "issue": "3", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> <PERSON><PERSON>, \"Radial distribution test feeders,\" IEEE Trans. Power Syst., vol. 6, no. 3, pp. 975, 985, Aug. 1991.", "links": null}}, "ref_entries": {"FIGREF0": {"type_str": "figure", "fig_num": "1", "num": null, "text": "Fig. 1. Schematic representation of the proposed linearization. (a) Values of in the complex plane. (b) Total error in percentage.", "uris": null}, "FIGREF1": {"type_str": "figure", "fig_num": "2", "num": null, "text": "Fig. 2. Error between the back-forward sweep algorithm and the proposed linear power flow. (a) Single phase case. (b) Three-phase unbalanced case.", "uris": null}}}}